import { Request, Response } from "express";
import { z } from "zod";
import { prisma } from "../index";
import { AppError } from "../middleware/errorHandler";
import slugify from "slugify";

// Validation schemas
const blogPostSchema = z.object({
  title: z.string().min(5).max(200),
  content: z.string().min(50),
  excerpt: z.string().max(300).optional(),
  featuredImageUrl: z.string().url().optional(),
  status: z.enum(["DRAFT", "PUBLISHED"]).optional(),
  categoryIds: z.array(z.string()).optional(),
  tagIds: z.array(z.string()).optional(),
});

const categorySchema = z.object({
  name: z.string().min(2).max(50),
});

const tagSchema = z.object({
  name: z.string().min(2).max(50),
});

const userUpdateSchema = z.object({
  role: z.enum(["USER", "ADMIN"]).optional(),
  emailVerified: z.boolean().optional(),
  profile: z
    .object({
      firstName: z.string().optional(),
      lastName: z.string().optional(),
      avatarUrl: z.string().optional(),
    })
    .optional(),
  credit: z
    .object({
      balance: z.number().optional(),
    })
    .optional(),
});

const getUsersQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  search: z.string().optional(),
  status: z.enum(["active", "inactive", "all"]).optional().default("all"),
  role: z.enum(["USER", "ADMIN", "all"]).optional().default("all"),
  sortBy: z
    .enum(["createdAt", "lastLogin", "email", "name"])
    .optional()
    .default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

// User Management
export const getAllUsers = async (req: Request, res: Response) => {
  try {
    // Validate query parameters
    const validationResult = getUsersQuerySchema.safeParse(req.query);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid query parameters",
          details: validationResult.error.errors,
        },
      });
    }

    const { page, limit, search, status, role, sortBy, sortOrder } =
      validationResult.data;
    const pageNumber = parseInt(page, 10);
    const limitNumber = parseInt(limit, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // Build filter conditions
    const where: any = {};

    // Search filter
    if (search) {
      where.OR = [
        {
          email: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          profile: {
            OR: [
              {
                firstName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
              {
                lastName: {
                  contains: search,
                  mode: "insensitive",
                },
              },
            ],
          },
        },
      ];
    }

    // Status filter
    if (status !== "all") {
      where.emailVerified = status === "active";
    }

    // Role filter
    if (role !== "all") {
      where.role = role;
    }

    // Build order by clause
    let orderBy: any = {};
    if (sortBy === "name") {
      orderBy = {
        profile: {
          firstName: sortOrder,
        },
      };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    // Get users with pagination
    const [users, totalUsers] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: true,
          subscriptions: {
            where: {
              status: "ACTIVE",
            },
            include: {
              plan: true,
            },
            orderBy: {
              createdAt: "desc",
            },
            take: 1,
          },
          credit: true,
        },
        orderBy,
        skip,
        take: limitNumber,
      }),
      prisma.user.count({ where }),
    ]);

    // Format response
    const formattedUsers = users.map((user) => ({
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
      profile: user.profile
        ? {
            firstName: user.profile.firstName || "",
            lastName: user.profile.lastName || "",
            fullName:
              `${user.profile.firstName || ""} ${user.profile.lastName || ""}`.trim() ||
              user.email.split("@")[0],
            avatarUrl: user.profile.avatarUrl || "",
          }
        : {
            firstName: "",
            lastName: "",
            fullName: user.email.split("@")[0],
            avatarUrl: "",
          },
      name: user.profile
        ? `${user.profile.firstName || ""} ${user.profile.lastName || ""}`.trim() ||
          user.email.split("@")[0]
        : user.email.split("@")[0],
      subscription: user.subscriptions[0] || null,
      plan: user.subscriptions[0]?.plan?.name || "Free",
      credit: user.credit || { balance: 0, spent: 0 },
      status: user.emailVerified ? "active" : "inactive",
      createdAt: user.createdAt,
      lastLogin: user.lastLogin,
      joinDate: user.createdAt.toISOString(),
      lastActive: user.lastLogin?.toISOString() || null,
      totalSpent: user.credit?.spent || 0,
    }));

    res.status(200).json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          total: totalUsers,
          page: pageNumber,
          limit: limitNumber,
          totalPages: Math.ceil(totalUsers / limitNumber),
          hasNextPage: pageNumber < Math.ceil(totalUsers / limitNumber),
          hasPrevPage: pageNumber > 1,
        },
        filters: {
          search,
          status,
          role,
          sortBy,
          sortOrder,
        },
      },
    });
  } catch (error) {
    console.error("Error getting users:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get users",
        details: error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
};

export const getUserById = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_USER_ID",
          message: "User ID is required",
        },
      });
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        subscriptions: {
          include: {
            plan: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        credit: true,
        payments: {
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        videoJobs: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
        imageJobs: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
        bgRemovalJobs: {
          orderBy: {
            createdAt: "desc",
          },
          take: 5,
        },
      },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user",
      },
    });
  }
};

export const updateUser = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_USER_ID",
          message: "User ID is required",
        },
      });
    }

    // Validate request body
    const validationResult = userUpdateSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: validationResult.error.errors,
        },
      });
    }

    const validatedData = validationResult.data;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        credit: true,
      },
    });

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Use transaction to update user and related data
    const updatedUser = await prisma.$transaction(async (tx) => {
      // Update user basic fields
      const userUpdateData: any = {};
      if (validatedData.role !== undefined) {
        userUpdateData.role = validatedData.role;
      }
      if (validatedData.emailVerified !== undefined) {
        userUpdateData.emailVerified = validatedData.emailVerified;
      }

      const user = await tx.user.update({
        where: { id: userId },
        data: userUpdateData,
      });

      // Update profile if provided
      if (validatedData.profile) {
        if (existingUser.profile) {
          await tx.userProfile.update({
            where: { userId },
            data: {
              firstName: validatedData.profile.firstName,
              lastName: validatedData.profile.lastName,
              avatarUrl: validatedData.profile.avatarUrl,
            },
          });
        } else {
          await tx.userProfile.create({
            data: {
              userId,
              firstName: validatedData.profile.firstName || "",
              lastName: validatedData.profile.lastName || "",
              avatarUrl: validatedData.profile.avatarUrl || "",
            },
          });
        }
      }

      // Update credit balance if provided
      if (validatedData.credit?.balance !== undefined) {
        if (existingUser.credit) {
          await tx.credit.update({
            where: { userId },
            data: {
              balance: validatedData.credit.balance,
            },
          });
        } else {
          await tx.credit.create({
            data: {
              userId,
              balance: validatedData.credit.balance,
              spent: 0,
            },
          });
        }
      }

      return user;
    });

    // Fetch updated user with all relations
    const userWithRelations = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        profile: true,
        credit: true,
        subscriptions: {
          where: { status: "ACTIVE" },
          include: { plan: true },
          orderBy: { createdAt: "desc" },
          take: 1,
        },
      },
    });

    // Format response to match frontend expectations
    const formattedUser = {
      id: userWithRelations!.id,
      email: userWithRelations!.email,
      role: userWithRelations!.role,
      emailVerified: userWithRelations!.emailVerified,
      profile: userWithRelations!.profile
        ? {
            firstName: userWithRelations!.profile.firstName || "",
            lastName: userWithRelations!.profile.lastName || "",
            fullName:
              `${userWithRelations!.profile.firstName || ""} ${userWithRelations!.profile.lastName || ""}`.trim() ||
              userWithRelations!.email.split("@")[0],
            avatarUrl: userWithRelations!.profile.avatarUrl || "",
          }
        : {
            firstName: "",
            lastName: "",
            fullName: userWithRelations!.email.split("@")[0],
            avatarUrl: "",
          },
      name: userWithRelations!.profile
        ? `${userWithRelations!.profile.firstName || ""} ${userWithRelations!.profile.lastName || ""}`.trim() ||
          userWithRelations!.email.split("@")[0]
        : userWithRelations!.email.split("@")[0],
      subscription: userWithRelations!.subscriptions[0] || null,
      plan: userWithRelations!.subscriptions[0]?.plan?.name || "Free",
      credit: userWithRelations!.credit || { balance: 0, spent: 0 },
      status: userWithRelations!.emailVerified ? "active" : "inactive",
      createdAt: userWithRelations!.createdAt,
      lastLogin: userWithRelations!.lastLogin,
      joinDate: userWithRelations!.createdAt.toISOString(),
      lastActive: userWithRelations!.lastLogin?.toISOString() || null,
      totalSpent: userWithRelations!.credit?.spent || 0,
    };

    res.status(200).json({
      success: true,
      data: formattedUser,
      message: "User updated successfully",
    });
  } catch (error) {
    console.error("Error updating user:", error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update user",
        details: error instanceof Error ? error.message : "Unknown error",
      },
    });
  }
};

export const deleteUser = async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_USER_ID",
          message: "User ID is required",
        },
      });
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          code: "USER_NOT_FOUND",
          message: "User not found",
        },
      });
    }

    // Delete user
    await prisma.user.delete({
      where: { id: userId },
    });

    res.status(200).json({
      success: true,
      message: "User deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete user",
      },
    });
  }
};

// Blog Management
export const createBlogPost = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = blogPostSchema.parse(req.body);

    // Generate slug
    let slug = slugify(validatedData.title, { lower: true, strict: true });

    // Check if slug already exists
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug },
    });

    if (existingPost) {
      // Append random string to make slug unique
      slug = `${slug}-${Math.random().toString(36).substring(2, 8)}`;
    }

    // Create blog post
    const post = await prisma.blogPost.create({
      data: {
        title: validatedData.title,
        slug,
        content: validatedData.content,
        excerpt: validatedData.excerpt,
        featuredImageUrl: validatedData.featuredImageUrl,
        status: validatedData.status || "DRAFT",
        publishedAt: validatedData.status === "PUBLISHED" ? new Date() : null,
        authorId: req.user.id,
      },
    });

    // Add categories if provided
    if (validatedData.categoryIds && validatedData.categoryIds.length > 0) {
      const categoryConnections = validatedData.categoryIds.map(
        (categoryId) => ({
          postId: post.id,
          categoryId,
        })
      );

      await prisma.blogPostCategory.createMany({
        data: categoryConnections,
      });
    }

    // Add tags if provided
    if (validatedData.tagIds && validatedData.tagIds.length > 0) {
      const tagConnections = validatedData.tagIds.map((tagId) => ({
        postId: post.id,
        tagId,
      }));

      await prisma.blogPostTag.createMany({
        data: tagConnections,
      });
    }

    // Get created post with categories and tags
    const createdPost = await prisma.blogPost.findUnique({
      where: { id: post.id },
      include: {
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    res.status(201).json({
      success: true,
      data: createdPost,
      message: "Blog post created successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create blog post",
      },
    });
  }
};

export const updateBlogPost = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_POST_ID",
          message: "Post ID is required",
        },
      });
    }

    // Validate request body
    const validatedData = blogPostSchema.parse(req.body);

    // Check if post exists
    const post = await prisma.blogPost.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        error: {
          code: "POST_NOT_FOUND",
          message: "Blog post not found",
        },
      });
    }

    // Check if title changed and update slug if needed
    let slug = post.slug;
    if (validatedData.title !== post.title) {
      slug = slugify(validatedData.title, { lower: true, strict: true });

      // Check if slug already exists
      const existingPost = await prisma.blogPost.findFirst({
        where: {
          slug,
          id: {
            not: postId,
          },
        },
      });

      if (existingPost) {
        // Append random string to make slug unique
        slug = `${slug}-${Math.random().toString(36).substring(2, 8)}`;
      }
    }

    // Update post
    const updatedPost = await prisma.blogPost.update({
      where: { id: postId },
      data: {
        title: validatedData.title,
        slug,
        content: validatedData.content,
        excerpt: validatedData.excerpt,
        featuredImageUrl: validatedData.featuredImageUrl,
        status: validatedData.status,
        publishedAt:
          validatedData.status === "PUBLISHED" && !post.publishedAt
            ? new Date()
            : post.publishedAt,
      },
    });

    // Update categories if provided
    if (validatedData.categoryIds) {
      // Delete existing category connections
      await prisma.blogPostCategory.deleteMany({
        where: { postId },
      });

      // Add new category connections
      if (validatedData.categoryIds.length > 0) {
        const categoryConnections = validatedData.categoryIds.map(
          (categoryId) => ({
            postId,
            categoryId,
          })
        );

        await prisma.blogPostCategory.createMany({
          data: categoryConnections,
        });
      }
    }

    // Update tags if provided
    if (validatedData.tagIds) {
      // Delete existing tag connections
      await prisma.blogPostTag.deleteMany({
        where: { postId },
      });

      // Add new tag connections
      if (validatedData.tagIds.length > 0) {
        const tagConnections = validatedData.tagIds.map((tagId) => ({
          postId,
          tagId,
        }));

        await prisma.blogPostTag.createMany({
          data: tagConnections,
        });
      }
    }

    // Get updated post with categories and tags
    const postWithRelations = await prisma.blogPost.findUnique({
      where: { id: postId },
      include: {
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
      },
    });

    res.status(200).json({
      success: true,
      data: postWithRelations,
      message: "Blog post updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update blog post",
      },
    });
  }
};

export const deleteBlogPost = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_POST_ID",
          message: "Post ID is required",
        },
      });
    }

    // Check if post exists
    const post = await prisma.blogPost.findUnique({
      where: { id: postId },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        error: {
          code: "POST_NOT_FOUND",
          message: "Blog post not found",
        },
      });
    }

    // Delete post
    await prisma.blogPost.delete({
      where: { id: postId },
    });

    res.status(200).json({
      success: true,
      message: "Blog post deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete blog post",
      },
    });
  }
};

export const getAllBlogPosts = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    const { page = "1", limit = "10", status, search } = req.query;

    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // Build filter conditions
    const where: any = {};

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          title: {
            contains: search as string,
            mode: "insensitive",
          },
        },
        {
          content: {
            contains: search as string,
            mode: "insensitive",
          },
        },
      ];
    }

    // Get posts
    const posts = await prisma.blogPost.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            email: true,
            profile: true,
          },
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limitNumber,
    });

    // Get total count for pagination
    const totalPosts = await prisma.blogPost.count({ where });

    // Format response
    const formattedPosts = posts.map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      featuredImageUrl: post.featuredImageUrl,
      status: post.status,
      publishedAt: post.publishedAt,
      createdAt: post.createdAt,
      author: post.author,
      categories: post.categories.map((c) => c.category),
      tags: post.tags.map((t) => t.tag),
    }));

    res.status(200).json({
      success: true,
      data: {
        posts: formattedPosts,
        pagination: {
          total: totalPosts,
          page: pageNumber,
          limit: limitNumber,
          totalPages: Math.ceil(totalPosts / limitNumber),
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get blog posts",
      },
    });
  }
};

// Category Management
export const createCategory = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = categorySchema.parse(req.body);

    // Generate slug
    const slug = slugify(validatedData.name, { lower: true, strict: true });

    // Check if category already exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: { slug },
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        error: {
          code: "CATEGORY_EXISTS",
          message: "Category with this name already exists",
        },
      });
    }

    // Create category
    const category = await prisma.blogCategory.create({
      data: {
        name: validatedData.name,
        slug,
      },
    });

    res.status(201).json({
      success: true,
      data: category,
      message: "Category created successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create category",
      },
    });
  }
};

// Tag Management
export const createTag = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Authentication required",
        },
      });
    }

    // Validate request body
    const validatedData = tagSchema.parse(req.body);

    // Generate slug
    const slug = slugify(validatedData.name, { lower: true, strict: true });

    // Check if tag already exists
    const existingTag = await prisma.blogTag.findUnique({
      where: { slug },
    });

    if (existingTag) {
      return res.status(400).json({
        success: false,
        error: {
          code: "TAG_EXISTS",
          message: "Tag with this name already exists",
        },
      });
    }

    // Create tag
    const tag = await prisma.blogTag.create({
      data: {
        name: validatedData.name,
        slug,
      },
    });

    res.status(201).json({
      success: true,
      data: tag,
      message: "Tag created successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create tag",
      },
    });
  }
};

export const getAllTags = async (req: Request, res: Response) => {
  try {
    const tags = await prisma.blogTag.findMany({
      orderBy: {
        name: "asc", // Optional: sorts alphabetically
      },
    });

    res.status(200).json({
      success: true,
      data: tags,
      message: "Tags fetched successfully",
    });
  } catch (error) {
    console.error("Error fetching tags:", error);

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch tags",
      },
    });
  }
};

export const getAnalyticsSummary = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;

    // Get date range based on timeRange parameter
    const startDate = getStartDateForRange(timeRange as string);

    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get new users in the selected time period
    const newUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get active subscriptions
    const activeSubscriptions = await prisma.userSubscription.count({
      where: {
        status: "ACTIVE",
      },
    });

    // Get total revenue
    const payments = await prisma.payment.findMany({
      where: {
        status: "COMPLETED",
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        amount: true,
      },
    });

    const totalRevenue = payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    // Get video generations count
    const videoGenerations = await prisma.generatedVideoJob.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get image generations count
    const imageGenerations = await prisma.generatedImageJob.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get background removals count
    const backgroundRemovals = await prisma.backgroundImageRemovalJob.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        newUsers,
        activeSubscriptions,
        totalRevenue,
        videoGenerations,
        imageGenerations,
        backgroundRemovals,
        timeRange,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get analytics data",
      },
    });
  }
};

// Helper function to determine start date based on time range
const getStartDateForRange = (timeRange: string): Date => {
  const now = new Date();
  switch (timeRange) {
    case "daily":
      return new Date(now.setDate(now.getDate() - 7)); // Last 7 days
    case "weekly":
      return new Date(now.setDate(now.getDate() - 28)); // Last 4 weeks
    case "monthly":
    default:
      return new Date(now.setMonth(now.getMonth() - 6)); // Last 6 months
  }
};

// Validation schema for settings
const settingsSchema = z.object({
  general: z
    .object({
      siteName: z.string().min(2).max(100).optional(),
      siteDescription: z.string().max(500).optional(),
      contactEmail: z.string().email().optional(),
      supportPhone: z.string().optional(),
      maintenanceMode: z.boolean().optional(),
    })
    .optional(),
  api: z
    .object({
      videoApiKey: z.string().optional(),
      imageApiKey: z.string().optional(),
      backgroundApiKey: z.string().optional(),
      videoApiEndpoint: z.string().url().optional(),
      imageApiEndpoint: z.string().url().optional(),
      backgroundApiEndpoint: z.string().url().optional(),
    })
    .optional(),
  email: z
    .object({
      smtpServer: z.string().optional(),
      smtpPort: z.string().optional(),
      smtpUsername: z.string().optional(),
      smtpPassword: z.string().optional(),
      senderEmail: z.string().email().optional(),
      senderName: z.string().optional(),
    })
    .optional(),
  payment: z
    .object({
      stripePublicKey: z.string().optional(),
      stripeSecretKey: z.string().optional(),
      paypalClientId: z.string().optional(),
      paypalSecret: z.string().optional(),
      enableStripe: z.boolean().optional(),
      enablePaypal: z.boolean().optional(),
      testMode: z.boolean().optional(),
    })
    .optional(),
  limits: z
    .object({
      freeVideoLimit: z.number().int().optional(),
      freeImageLimit: z.number().int().optional(),
      freeBackgroundLimit: z.number().int().optional(),
      starterVideoLimit: z.number().int().optional(),
      starterImageLimit: z.number().int().optional(),
      starterBackgroundLimit: z.number().int().optional(),
      proVideoLimit: z.number().int().optional(),
      proImageLimit: z.number().int().optional(),
      proBackgroundLimit: z.number().int().optional(),
      businessVideoLimit: z.union([z.number().int(), z.string()]).optional(),
      businessImageLimit: z.union([z.number().int(), z.string()]).optional(),
      businessBackgroundLimit: z
        .union([z.number().int(), z.string()])
        .optional(),
    })
    .optional(),
  blog: z
    .object({
      postsPerPage: z.number().int().optional(),
      enableComments: z.boolean().optional(),
      moderateComments: z.boolean().optional(),
      allowGuestComments: z.boolean().optional(),
      featuredPostsCount: z.number().int().optional(),
    })
    .optional(),
});

export const getSettings = async (req: Request, res: Response) => {
  try {
    // Get all settings from the database
    const settings = await prisma.siteSetting.findMany();

    // Convert to a structured object
    const formattedSettings = settings.reduce(
      (acc, setting) => {
        const [category, key] = setting.key.split(".");
        if (!acc[category]) acc[category] = {};
        acc[category][key] = setting.value;
        return acc;
      },
      {} as Record<string, Record<string, any>>
    );

    res.status(200).json({
      success: true,
      data: formattedSettings,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get settings",
      },
    });
  }
};

export const updateSettings = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validatedData = settingsSchema.parse(req.body);

    // Prepare settings updates
    const settingsToUpdate: { key: string; value: any }[] = [];

    // Process each category
    Object.entries(validatedData).forEach(([category, settings]) => {
      Object.entries(settings).forEach(([key, value]) => {
        settingsToUpdate.push({
          key: `${category}.${key}`,
          value:
            typeof value === "object" ? JSON.stringify(value) : String(value),
        });
      });
    });

    // Update settings in database
    await Promise.all(
      settingsToUpdate.map(async (setting) => {
        await prisma.siteSetting.upsert({
          where: { key: setting.key },
          update: { value: setting.value },
          create: { key: setting.key, value: setting.value },
        });
      })
    );

    res.status(200).json({
      success: true,
      message: "Settings updated successfully",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid input data",
          details: error.errors,
        },
      });
    }

    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update settings",
      },
    });
  }
};

// Additional analytics functions
export const getRevenueAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;

    // Get date range based on timeRange parameter
    const startDate = getStartDateForRange(timeRange as string);

    // Get revenue data grouped by time period
    let revenueData;

    if (timeRange === "daily") {
      // Daily revenue for the last 7 days
      revenueData = await getDailyRevenue(startDate);
    } else if (timeRange === "weekly") {
      // Weekly revenue for the last 4 weeks
      revenueData = await getWeeklyRevenue(startDate);
    } else {
      // Monthly revenue for the last 6 months
      revenueData = await getMonthlyRevenue(startDate);
    }

    // Get revenue by plan
    const revenueByPlan = await getRevenueByPlan(startDate);

    res.status(200).json({
      success: true,
      data: {
        timeRange,
        revenueData,
        revenueByPlan,
        totalRevenue: revenueData.reduce((sum, item) => sum + item.amount, 0),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get revenue analytics",
      },
    });
  }
};

export const getUsageAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;

    // Get date range based on timeRange parameter
    const startDate = getStartDateForRange(timeRange as string);

    // Get usage data for each feature
    const videoGenerationData = await getFeatureUsageData(
      "video",
      timeRange as string,
      startDate
    );
    const imageGenerationData = await getFeatureUsageData(
      "image",
      timeRange as string,
      startDate
    );
    const backgroundRemovalData = await getFeatureUsageData(
      "backgroundRemoval",
      timeRange as string,
      startDate
    );

    // Calculate distribution percentages
    const totalUsage =
      videoGenerationData.total +
      imageGenerationData.total +
      backgroundRemovalData.total;

    const usageDistribution = {
      videoGeneration:
        totalUsage > 0
          ? Math.round((videoGenerationData.total / totalUsage) * 100)
          : 0,
      imageGeneration:
        totalUsage > 0
          ? Math.round((imageGenerationData.total / totalUsage) * 100)
          : 0,
      backgroundRemoval:
        totalUsage > 0
          ? Math.round((backgroundRemovalData.total / totalUsage) * 100)
          : 0,
    };

    res.status(200).json({
      success: true,
      data: {
        timeRange,
        videoGeneration: videoGenerationData,
        imageGeneration: imageGenerationData,
        backgroundRemoval: backgroundRemovalData,
        usageDistribution,
        totalUsage,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get usage analytics",
      },
    });
  }
};

export const getUserAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "monthly" } = req.query;

    // Get date range based on timeRange parameter
    const startDate = getStartDateForRange(timeRange as string);

    // Get user growth data
    const userGrowthData = await getUserGrowthData(
      timeRange as string,
      startDate
    );

    // Get plan distribution
    const planDistribution = await getPlanDistribution();

    // Get user retention data
    const retentionData = await getUserRetentionData();

    // Get user activity metrics
    const activityMetrics = await getUserActivityMetrics();

    res.status(200).json({
      success: true,
      data: {
        timeRange,
        userGrowth: userGrowthData,
        planDistribution,
        retention: retentionData,
        activity: activityMetrics,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get user analytics",
      },
    });
  }
};

// Helper functions for analytics

// Revenue analytics helpers
async function getDailyRevenue(startDate: Date) {
  const payments = await prisma.payment.findMany({
    where: {
      status: "COMPLETED",
      createdAt: {
        gte: startDate,
      },
    },
    select: {
      amount: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  // Group by day
  const dailyRevenue: { date: string; amount: number }[] = [];
  const days = 7; // Last 7 days

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateString = date.toISOString().split("T")[0];

    const dayPayments = payments.filter((payment) => {
      const paymentDate = payment.createdAt.toISOString().split("T")[0];
      return paymentDate === dateString;
    });

    const amount = dayPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    dailyRevenue.push({
      date: dateString,
      amount,
    });
  }

  return dailyRevenue;
}

async function getWeeklyRevenue(startDate: Date) {
  const payments = await prisma.payment.findMany({
    where: {
      status: "COMPLETED",
      createdAt: {
        gte: startDate,
      },
    },
    select: {
      amount: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  // Group by week
  const weeklyRevenue: { date: string; amount: number }[] = [];
  const weeks = 4; // Last 4 weeks

  for (let i = 0; i < weeks; i++) {
    const weekStart = new Date(startDate);
    weekStart.setDate(weekStart.getDate() + i * 7);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);

    const weekPayments = payments.filter((payment) => {
      return payment.createdAt >= weekStart && payment.createdAt <= weekEnd;
    });

    const amount = weekPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    weeklyRevenue.push({
      date: `Week ${i + 1}`,
      amount,
    });
  }

  return weeklyRevenue;
}

async function getMonthlyRevenue(startDate: Date) {
  const payments = await prisma.payment.findMany({
    where: {
      status: "COMPLETED",
      createdAt: {
        gte: startDate,
      },
    },
    select: {
      amount: true,
      createdAt: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  // Group by month
  const monthlyRevenue: { date: string; amount: number }[] = [];
  const months = 6; // Last 6 months

  for (let i = 0; i < months; i++) {
    const monthStart = new Date(startDate);
    monthStart.setMonth(monthStart.getMonth() + i);
    monthStart.setDate(1);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);
    monthEnd.setDate(0);

    const monthPayments = payments.filter((payment) => {
      return payment.createdAt >= monthStart && payment.createdAt <= monthEnd;
    });

    const amount = monthPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    monthlyRevenue.push({
      date: monthNames[monthStart.getMonth()],
      amount,
    });
  }

  return monthlyRevenue;
}

async function getRevenueByPlan(startDate: Date) {
  // Get all payments in the period
  const payments = await prisma.payment.findMany({
    where: {
      status: "COMPLETED",
      createdAt: {
        gte: startDate,
      },
    },
  });

  // Get all active subscriptions
  const subscriptions = await prisma.userSubscription.findMany({
    include: {
      plan: true,
    },
  });

  // Map user IDs to their subscription plans
  const userPlans = new Map<string, string>();
  subscriptions.forEach((sub) => {
    if (sub.status === "ACTIVE") {
      userPlans.set(sub.userId, sub.plan.name);
    }
  });

  // Group payments by plan type (using user's current plan as a proxy)
  const planRevenue: Record<string, number> = {};
  let totalRevenue = 0;

  // Initialize with common plan types
  planRevenue["Free"] = 0;
  planRevenue["Starter"] = 0;
  planRevenue["Pro"] = 0;
  planRevenue["Business"] = 0;
  planRevenue["Credit Purchases"] = 0;

  payments.forEach((payment) => {
    const planName = userPlans.get(payment.userId) || "Unknown";
    // For simplicity, we'll categorize all payments as either subscription payments or credit purchases
    // In a real app, you'd have more specific categorization based on payment metadata

    // Assume payments under $30 are credit purchases, others are subscription payments
    if (payment.amount < 30) {
      planRevenue["Credit Purchases"] =
        (planRevenue["Credit Purchases"] || 0) + payment.amount;
    } else {
      planRevenue[planName] = (planRevenue[planName] || 0) + payment.amount;
    }

    totalRevenue += payment.amount;
  });

  // Calculate percentages
  const revenueByPlan = Object.entries(planRevenue).map(([plan, amount]) => ({
    plan,
    amount,
    percentage:
      totalRevenue > 0 ? Math.round((amount / totalRevenue) * 100) : 0,
  }));

  return revenueByPlan;
}

// Usage analytics helpers
async function getFeatureUsageData(
  featureType: string,
  timeRange: string,
  startDate: Date
) {
  let data: { date: string; count: number }[] = [];
  let total = 0;

  if (featureType === "video") {
    // Video generation usage
    const videoJobs = await prisma.generatedVideoJob.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    total = videoJobs.length;
    data = groupJobsByTimePeriod(videoJobs, timeRange, startDate);
  } else if (featureType === "image") {
    // Image generation usage
    const imageJobs = await prisma.generatedImageJob.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    total = imageJobs.length;
    data = groupJobsByTimePeriod(imageJobs, timeRange, startDate);
  } else if (featureType === "backgroundRemoval") {
    // Background removal usage
    const bgRemovalJobs = await prisma.backgroundImageRemovalJob.findMany({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    total = bgRemovalJobs.length;
    data = groupJobsByTimePeriod(bgRemovalJobs, timeRange, startDate);
  }

  return { data, total };
}

function groupJobsByTimePeriod(
  jobs: { createdAt: Date }[],
  timeRange: string,
  startDate: Date
) {
  if (timeRange === "daily") {
    // Group by day for the last 7 days
    return groupByDay(jobs, startDate, 7);
  } else if (timeRange === "weekly") {
    // Group by week for the last 4 weeks
    return groupByWeek(jobs, startDate, 4);
  } else {
    // Group by month for the last 6 months
    return groupByMonth(jobs, startDate, 6);
  }
}

function groupByDay(
  jobs: { createdAt: Date }[],
  startDate: Date,
  days: number
) {
  const result: { date: string; count: number }[] = [];

  for (let i = 0; i < days; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateString = date.toISOString().split("T")[0];

    const dayJobs = jobs.filter((job) => {
      const jobDate = job.createdAt.toISOString().split("T")[0];
      return jobDate === dateString;
    });

    result.push({
      date: `Day ${i + 1}`,
      count: dayJobs.length,
    });
  }

  return result;
}

function groupByWeek(
  jobs: { createdAt: Date }[],
  startDate: Date,
  weeks: number
) {
  const result: { date: string; count: number }[] = [];

  for (let i = 0; i < weeks; i++) {
    const weekStart = new Date(startDate);
    weekStart.setDate(weekStart.getDate() + i * 7);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);

    const weekJobs = jobs.filter((job) => {
      return job.createdAt >= weekStart && job.createdAt <= weekEnd;
    });

    result.push({
      date: `Week ${i + 1}`,
      count: weekJobs.length,
    });
  }

  return result;
}

function groupByMonth(
  jobs: { createdAt: Date }[],
  startDate: Date,
  months: number
) {
  const result: { date: string; count: number }[] = [];
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  for (let i = 0; i < months; i++) {
    const monthStart = new Date(startDate);
    monthStart.setMonth(monthStart.getMonth() + i);
    monthStart.setDate(1);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);
    monthEnd.setDate(0);

    const monthJobs = jobs.filter((job) => {
      return job.createdAt >= monthStart && job.createdAt <= monthEnd;
    });

    result.push({
      date: monthNames[monthStart.getMonth()],
      count: monthJobs.length,
    });
  }

  return result;
}

// User analytics helpers
async function getUserGrowthData(timeRange: string, startDate: Date) {
  const users = await prisma.user.findMany({
    where: {
      createdAt: {
        gte: startDate,
      },
    },
    select: {
      createdAt: true,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  let growthData: { date: string; count: number }[];

  if (timeRange === "daily") {
    growthData = groupByDay(users, startDate, 7);
  } else if (timeRange === "weekly") {
    growthData = groupByWeek(users, startDate, 4);
  } else {
    growthData = groupByMonth(users, startDate, 6);
  }

  return growthData;
}

async function getPlanDistribution() {
  // Get all active subscriptions grouped by plan
  const subscriptionsByPlan = await prisma.userSubscription.groupBy({
    by: ["planId"],
    where: {
      status: "ACTIVE",
    },
    _count: {
      id: true,
    },
  });

  // Get plan details
  const plans = await prisma.subscriptionPlan.findMany();

  // Get total users count
  const totalUsers = await prisma.user.count();

  // Count users without active subscriptions (free plan)
  const usersWithSubscriptions = await prisma.user.count({
    where: {
      subscriptions: {
        some: {
          status: "ACTIVE",
        },
      },
    },
  });

  const freeUsers = totalUsers - usersWithSubscriptions;

  // Format plan distribution data
  const planDistribution = [
    {
      plan: "Free",
      users: freeUsers,
      percentage: Math.round((freeUsers / totalUsers) * 100),
    },
    ...subscriptionsByPlan.map((subscription) => {
      const plan = plans.find((p) => p.id === subscription.planId);
      return {
        plan: plan?.name || "Unknown",
        users: subscription._count.id,
        percentage: Math.round((subscription._count.id / totalUsers) * 100),
      };
    }),
  ];

  return planDistribution;
}

async function getUserRetentionData() {
  // This would be a complex calculation in a real app
  // For demo purposes, we'll return mock data
  return [
    { period: "1 day", rate: 95 },
    { period: "7 days", rate: 82 },
    { period: "30 days", rate: 68 },
    { period: "90 days", rate: 42 },
  ];
}

async function getUserActivityMetrics() {
  // Get daily active users (users who logged in today)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const dailyActiveUsers = await prisma.user.count({
    where: {
      lastLogin: {
        gte: today,
      },
    },
  });

  // Get monthly active users (users who logged in within the last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const monthlyActiveUsers = await prisma.user.count({
    where: {
      lastLogin: {
        gte: thirtyDaysAgo,
      },
    },
  });

  // For demo purposes, we'll use mock data for the other metrics
  return {
    dailyActiveUsers,
    monthlyActiveUsers,
    averageSessionDuration: 18, // minutes
    averageActionsPerSession: 5.3,
  };
}

// Dashboard stats endpoint
export const getAdminStats = async (req: Request, res: Response) => {
  try {
    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get active users (logged in within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const activeUsers = await prisma.user.count({
      where: {
        lastLogin: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Get total revenue
    const payments = await prisma.payment.findMany();
    const totalRevenue = payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    // Get monthly recurring revenue (MRR)
    const currentMonth = new Date();
    currentMonth.setDate(1); // First day of current month
    currentMonth.setHours(0, 0, 0, 0);

    const monthlyPayments = payments.filter(
      (payment) => payment.createdAt >= currentMonth
    );
    const mrr = monthlyPayments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    // Get job counts
    const videoJobs = await prisma.generatedVideoJob.count();
    const imageJobs = await prisma.generatedImageJob.count();
    const backgroundRemovalJobs =
      await prisma.backgroundImageRemovalJob.count();
    const totalJobs = videoJobs + imageJobs + backgroundRemovalJobs;

    // Get active subscriptions
    const activeSubscriptions = await prisma.userSubscription.count({
      where: {
        status: "ACTIVE",
      },
    });

    // Get blog posts count
    const blogPosts = await prisma.blogPost.count();

    // Calculate growth rates (mock data for now)
    const userGrowth = 12.5; // percentage
    const revenueGrowth = 15.2; // percentage

    // Get active and completed jobs
    const activeJobs =
      (await prisma.generatedVideoJob.count({
        where: {
          status: {
            in: ["PENDING", "PROCESSING"],
          },
        },
      })) +
      (await prisma.generatedImageJob.count({
        where: {
          status: {
            in: ["PENDING", "PROCESSING"],
          },
        },
      })) +
      (await prisma.backgroundImageRemovalJob.count({
        where: {
          status: {
            in: ["PENDING", "PROCESSING"],
          },
        },
      }));

    const completedJobs =
      (await prisma.generatedVideoJob.count({
        where: {
          status: "COMPLETED",
        },
      })) +
      (await prisma.generatedImageJob.count({
        where: {
          status: "COMPLETED",
        },
      })) +
      (await prisma.backgroundImageRemovalJob.count({
        where: {
          status: "COMPLETED",
        },
      }));

    // Calculate total credits used
    const totalCreditsUsed = await prisma.creditTransaction.aggregate({
      _sum: {
        amount: true,
      },
      where: {
        type: {
          equals: "USAGE", // Using USAGE instead of DEBIT as per the schema
        },
      },
    });

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        activeUsers,
        totalRevenue,
        monthlyRevenue: mrr,
        totalCreditsUsed: totalCreditsUsed._sum?.amount || 0,
        videoGenerations: videoJobs,
        imageGenerations: imageJobs,
        backgroundRemovals: backgroundRemovalJobs,
        activeSubscriptions,
        activeJobs,
        completedJobs,
        blogPosts,
        userGrowth,
        revenueGrowth,
      },
    });
  } catch (error) {
    console.error("Error getting admin stats:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get admin statistics",
      },
    });
  }
};

// Recent users endpoint
export const getRecentUsers = async (req: Request, res: Response) => {
  try {
    const { limit = "5" } = req.query;
    const limitNumber = parseInt(limit as string, 10);

    const recentUsers = await prisma.user.findMany({
      include: {
        profile: true,
        subscriptions: {
          where: {
            status: "ACTIVE",
          },
          include: {
            plan: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 1,
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limitNumber,
    });

    const formattedUsers = recentUsers.map((user) => ({
      id: user.id,
      name: user.profile
        ? `${user.profile.firstName} ${user.profile.lastName}`
        : user.email.split("@")[0],
      email: user.email,
      role: user.role,
      plan: user.subscriptions[0]?.plan?.name || "Free",
      joinDate: user.createdAt.toISOString(),
      status: user.emailVerified ? "active" : "inactive",
    }));

    res.status(200).json({
      success: true,
      data: formattedUsers,
    });
  } catch (error) {
    console.error("Error getting recent users:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get recent users",
      },
    });
  }
};

// Recent jobs endpoint
export const getRecentJobs = async (req: Request, res: Response) => {
  try {
    const { limit = "5" } = req.query;
    const limitNumber = parseInt(limit as string, 10);

    // Get recent video jobs
    const videoJobs = await prisma.generatedVideoJob.findMany({
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limitNumber,
    });

    // Get recent image jobs
    const imageJobs = await prisma.generatedImageJob.findMany({
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limitNumber,
    });

    // Get recent background removal jobs
    const bgRemovalJobs = await prisma.backgroundImageRemovalJob.findMany({
      include: {
        user: true,
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limitNumber,
    });

    // Combine and format jobs
    const allJobs = [
      ...videoJobs.map((job) => ({
        id: job.id,
        type: "video",
        user: job.user.email,
        userId: job.userId,
        date: job.createdAt.toISOString(),
        status: job.status.toLowerCase(),
      })),
      ...imageJobs.map((job) => ({
        id: job.id,
        type: "image",
        user: job.user.email,
        userId: job.userId,
        date: job.createdAt.toISOString(),
        status: job.status.toLowerCase(),
      })),
      ...bgRemovalJobs.map((job: any) => ({
        id: job.id,
        type: "bgRemoval",
        user: job.user.email,
        userId: job.userId,
        date: job.createdAt.toISOString(),
        status: job.status.toLowerCase(),
      })),
    ];

    // Sort by date and limit
    const recentJobs = allJobs
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limitNumber);

    res.status(200).json({
      success: true,
      data: recentJobs,
    });
  } catch (error) {
    console.error("Error getting recent jobs:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get recent jobs",
      },
    });
  }
};

// Payment config schema
const paymentConfigSchema = z.object({
  stripeEnabled: z.boolean().optional(),
  stripePublicKey: z.string().optional(),
  stripeSecretKey: z.string().optional(),
  stripeWebhookSecret: z.string().optional(),
  paypalEnabled: z.boolean().optional(),
  paypalClientId: z.string().optional(),
  paypalClientSecret: z.string().optional(),
  paypalWebhookId: z.string().optional(),
  testMode: z.boolean().optional(),
});

// Payment config endpoints
export const getPaymentConfig = async (req: Request, res: Response) => {
  try {
    // Get payment settings from database
    const settings = await prisma.siteSetting.findFirst({
      where: { key: "payment" },
    });

    // Default config if not found
    const paymentConfig = settings
      ? JSON.parse(settings.value)
      : {
          stripe: {
            enabled: false,
            isLive: false,
            apiKey: "",
            secretKey: "",
            webhookSecret: "",
          },
          paypal: {
            enabled: false,
            isLive: false,
            clientId: "",
            clientSecret: "",
            webhookId: "",
          },
        };

    res.status(200).json({
      success: true,
      data: paymentConfig,
    });
  } catch (error) {
    console.error("Error getting payment config:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get payment configuration",
      },
    });
  }
};

export const updatePaymentConfig = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = paymentConfigSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payment configuration data",
          details: validationResult.error.errors,
        },
      });
    }

    const {
      stripeEnabled,
      stripePublicKey,
      stripeSecretKey,
      stripeWebhookSecret,
      paypalEnabled,
      paypalClientId,
      paypalClientSecret,
      paypalWebhookId,
      testMode,
    } = validationResult.data;

    // Get existing config
    let settings = await prisma.siteSetting.findFirst({
      where: { key: "payment" },
    });

    // Parse existing config or create default
    const existingConfig = settings
      ? JSON.parse(settings.value)
      : {
          stripe: {
            enabled: false,
            isLive: false,
            apiKey: "",
            secretKey: "",
            webhookSecret: "",
          },
          paypal: {
            enabled: false,
            isLive: false,
            clientId: "",
            clientSecret: "",
            webhookId: "",
          },
        };

    // Update config with new values
    const updatedConfig = {
      stripe: {
        ...existingConfig.stripe,
        enabled:
          stripeEnabled !== undefined
            ? stripeEnabled
            : existingConfig.stripe.enabled,
        isLive:
          testMode !== undefined ? !testMode : existingConfig.stripe.isLive,
        apiKey: stripePublicKey || existingConfig.stripe.apiKey,
        secretKey: stripeSecretKey || existingConfig.stripe.secretKey,
        webhookSecret:
          stripeWebhookSecret || existingConfig.stripe.webhookSecret,
      },
      paypal: {
        ...existingConfig.paypal,
        enabled:
          paypalEnabled !== undefined
            ? paypalEnabled
            : existingConfig.paypal.enabled,
        isLive:
          testMode !== undefined ? !testMode : existingConfig.paypal.isLive,
        clientId: paypalClientId || existingConfig.paypal.clientId,
        clientSecret: paypalClientSecret || existingConfig.paypal.clientSecret,
        webhookId: paypalWebhookId || existingConfig.paypal.webhookId,
      },
    };

    // Save updated config
    if (settings) {
      await prisma.siteSetting.update({
        where: { id: settings.id },
        data: { value: JSON.stringify(updatedConfig) },
      });
    } else {
      await prisma.siteSetting.create({
        data: {
          key: "payment",
          value: JSON.stringify(updatedConfig),
        },
      });
    }

    res.status(200).json({
      success: true,
      data: updatedConfig,
    });
  } catch (error) {
    console.error("Error updating payment config:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update payment configuration",
      },
    });
  }
};

// Subscription Plan validation schema
const subscriptionPlanSchema = z.object({
  name: z.string().min(2).max(50),
  displayName: z.string().min(2).max(100),
  description: z.string().min(10),
  price: z.number().positive(),
  currency: z.string().default("USD"),
  interval: z.enum(["month", "year"]),
  features: z
    .object({
      videoGenerationQuota: z.number().int().min(0),
      imageGenerationQuota: z.number().int().min(0),
      backgroundRemovalQuota: z.number().int().min(0),
    })
    .and(z.record(z.string(), z.any())),
  creditsAmount: z.number().int().min(0),
  featureHighlights: z
    .array(
      z.object({
        title: z.string(),
        description: z.string().optional(),
        icon: z.string().optional(),
      })
    )
    .default([]),
  colorScheme: z.string().optional(),
  isFeatured: z.boolean().default(false),
  sortOrder: z.number().int().default(0),
  stripePriceId: z.string().optional(),
  paypalPlanId: z.string().optional(),
  isActive: z.boolean().default(true),
});

// Get all subscription plans
export const getAllSubscriptionPlans = async (req: Request, res: Response) => {
  try {
    const plans = await prisma.subscriptionPlan.findMany({
      orderBy: {
        price: "asc",
      },
    });

    res.status(200).json({
      success: true,
      data: plans,
    });
  } catch (error) {
    console.error("Error getting subscription plans:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plans",
      },
    });
  }
};

// Get subscription plan by ID
export const getSubscriptionPlanById = async (req: Request, res: Response) => {
  try {
    const { planId } = req.params;

    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!plan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error("Error getting subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get subscription plan",
      },
    });
  }
};

// Create subscription plan
export const createSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = subscriptionPlanSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid subscription plan data",
          details: validationResult.error.errors,
        },
      });
    }

    const planData = validationResult.data;

    // Create plan in database
    const plan = await prisma.subscriptionPlan.create({
      data: {
        name: planData.name,
        price: planData.price,
        currency: planData.currency,
        interval: planData.interval,
        features: planData.features,
        stripePriceId: planData.stripePriceId,
        paypalPlanId: planData.paypalPlanId,
        isActive: planData.isActive,
        description: planData.description,
        featureHighlights: planData.featureHighlights,
        colorScheme: planData.colorScheme,
        isFeatured: planData.isFeatured,
        sortOrder: planData.sortOrder,
        displayName: planData.displayName,
        creditsAmount: planData.creditsAmount,
      },
    });

    res.status(201).json({
      success: true,
      data: plan,
    });
  } catch (error) {
    console.error("Error creating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create subscription plan",
      },
    });
  }
};

// Update subscription plan
export const updateSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    const { planId } = req.params;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Validate request body
    const validationResult = subscriptionPlanSchema
      .partial()
      .safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid subscription plan data",
          details: validationResult.error.errors,
        },
      });
    }

    const planData = validationResult.data;

    // Update plan in database
    const updatedPlan = await prisma.subscriptionPlan.update({
      where: { id: planId },
      data: planData,
    });

    res.status(200).json({
      success: true,
      data: updatedPlan,
    });
  } catch (error) {
    console.error("Error updating subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update subscription plan",
      },
    });
  }
};

// Delete subscription plan
export const deleteSubscriptionPlan = async (req: Request, res: Response) => {
  try {
    const { planId } = req.params;

    // Check if plan exists
    const existingPlan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!existingPlan) {
      return res.status(404).json({
        success: false,
        error: {
          code: "PLAN_NOT_FOUND",
          message: "Subscription plan not found",
        },
      });
    }

    // Check if plan is in use by any subscriptions
    const activeSubscriptions = await prisma.userSubscription.count({
      where: {
        planId: planId,
        status: "ACTIVE",
      },
    });

    if (activeSubscriptions > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: "PLAN_IN_USE",
          message: "Cannot delete plan that has active subscriptions",
        },
      });
    }

    // Delete plan from database
    await prisma.subscriptionPlan.delete({
      where: { id: planId },
    });

    res.status(200).json({
      success: true,
      data: { success: true },
    });
  } catch (error) {
    console.error("Error deleting subscription plan:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete subscription plan",
      },
    });
  }
};
