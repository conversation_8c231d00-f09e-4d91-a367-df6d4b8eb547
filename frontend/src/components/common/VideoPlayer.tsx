import { cn } from "@/lib/utils";

interface VideoPlayerProps {
  src: string;
  className?: string;
  containerClassName?: string;
  showGradientBorder?: boolean;
  aspectRatio?: "video" | "square" | "vertical" | "auto";
  ariaLabel?: string;
  isYouTube?: boolean;
}

const VideoPlayer = ({
  src,
  className,
  containerClassName,
  showGradientBorder = true,
  aspectRatio = "video",
  ariaLabel = "Video player",
  isYouTube = false,
}: VideoPlayerProps) => {
  const aspectRatioClasses = {
    video: "aspect-video",
    square: "aspect-square",
    vertical: "aspect-[9/16]",
    auto: "aspect-auto",
  };


  return (
    <div
      className={cn(
        "w-full h-full relative",
        aspectRatioClasses[aspectRatio],
        containerClassName
      )}
    >
      {showGradientBorder && (
        <>
          <div className="absolute -inset-[1px] bg-gradient-to-r from-[#2C9BF7]/30 to-[#8054F3]/30 rounded-lg blur-[2px]" />
          <div className="absolute -inset-[1px] bg-gradient-to-r from-[#2C9BF7]/10 to-[#8054F3]/10 rounded-lg blur-[8px]" />
        </>
      )}
      {isYouTube ? (
        <iframe
          src={src}
          className={cn(
            "relative w-full h-full rounded-lg bg-black/20 backdrop-blur-sm",
            className
          )}
          allow="enablejsapi=1; playsinline=1; modestbranding=1; rel=0; controls=1; mute=1'; accelerometer; autoplay=1; loop=1; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          title={ariaLabel}
        />
      ) : (
        <video
          src={src}
          autoPlay
          muted
          loop
          playsInline
          preload="metadata"
          className={cn(
            "relative w-full h-full object-cover rounded-lg bg-black/20 backdrop-blur-sm",
            className
          )}
          aria-label={ariaLabel}
        >
          <track kind="captions" />
        </video>
      )}
    </div>
  );
};

export default VideoPlayer; 