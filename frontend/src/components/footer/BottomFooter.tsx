import GradientText from "../common/GradientText";
import { Text } from "../ui/text";
import { Link } from "react-router-dom";

export default function BottomFooter() {
  const currentYear = new Date().getFullYear();
  return (
    <>
      <Text variant={"small_title"} className="text-white">
        © {currentYear}{" "}
        <Link to={"/"}>
          <GradientText>MiragicAI</GradientText>
        </Link>
        . All Rights Reserved.
      </Text>
      <div className="flex justify-center items-center gap-2">
        <Text
          variant={"small_title"}
        >
          <Link to={"/coming-soon"}>
            <GradientText>Terms of Service</GradientText>
          </Link>
        </Text>
        <span>|</span>
        <Text variant={"small_title"}>
          <Link to={"/coming-soon"}>
            <GradientText>Privacy & Policy</GradientText>
          </Link>
        </Text>
      </div>
    </>
  );
}
