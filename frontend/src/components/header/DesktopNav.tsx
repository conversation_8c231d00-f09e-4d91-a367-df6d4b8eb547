import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "../ui/button";
import { Link } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import useOutsideClick from "@/hooks/useOutsideClick";
import React, { useState, useRef } from "react";
import type { DropdownItem } from "../common/DropdownMenu";
import DropdownMenu from "../common/DropdownMenu";
import CompanySubNav from "./CompanySubNav";
import { navLinks, products, resources } from "../common/variable";
import { LanguageSubNav } from "./LanguageSubNav";
import { UPLOAD_URL } from "@/services/api.service";

interface DesktopNavProps {
  setIsShowLoginModal: React.Dispatch<React.SetStateAction<boolean>>;
  setIsShowSignUpModal: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DesktopNav({
  setIsShowLoginModal,
  setIsShowSignUpModal,
}: DesktopNavProps) {
  const { user, isAuthenticated } = useApp();

  const [openDropdown, setOpenDropdown] = useState<
    "products" | "resources" | "company" | "language" | null
  >(null);

  const dropdownsContainerRef = useRef<HTMLDivElement>(null);
  useOutsideClick(dropdownsContainerRef, () => {
    setOpenDropdown(null);
  });

  const closeAllDropdowns = () => {
    setOpenDropdown(null);
  };

  const handleDropdownItemClick = (href: string) => {
    window.location.href = href;
    closeAllDropdowns();
  };

  const handleMouseEnterDropdown = (
    dropdownType: "products" | "resources" | "company" | "language"
  ) => {
    setOpenDropdown(dropdownType);
  };

  return (
    <div className="flex items-center gap-16 w-full">
      <div className="flex items-center gap-5 justify-between w-full">
        <Link to="/" className="flex items-center gap-2">
          <img src="/png/new_miragic_logo.png" className="w-auto h-[42px]" />
        </Link>
        <nav
          className="hidden lg:flex items-center gap-4 xl:gap-14 xl:text-base text-sm"
          ref={dropdownsContainerRef}
        >
          <DropdownMenu
            title="Products"
            items={products}
            isOpen={openDropdown === "products"}
            onMouseEnter={() => handleMouseEnterDropdown("products")}
            onMouseLeave={closeAllDropdowns}
            onItemClick={handleDropdownItemClick}
            renderItem={(
              product: DropdownItem,
              index: number,
              clickHandler: (href: string) => void
            ) => (
              <div
                key={index}
                onClick={() => clickHandler(product.href)}
                className="flex items-center gap-3 bg-gray-800/40 px-7 py-3 hover:bg-gray-800/50 rounded-4xl border border-gray-600 cursor-pointer transition-colors duration-150"
              >
                <div className="flex-1 min-w-0">
                  <h4 className="text-white font-medium text-sm mb-1">
                    {product.name}
                  </h4>
                  <p className="text-[#F5F5F7] text-xs leading-relaxed">
                    {product.description}
                  </p>
                </div>
                <div
                  className={`w-10 h-10 ${product.color} rounded-lg flex items-center justify-center flex-shrink-0`}
                >
                  <div className="w-5 h-5 bg-white rounded-sm opacity-90"></div>
                </div>
              </div>
            )}
          />

          <DropdownMenu
            title="Resources"
            items={resources}
            isOpen={openDropdown === "resources"}
            onMouseEnter={() => handleMouseEnterDropdown("resources")}
            onMouseLeave={closeAllDropdowns}
            onItemClick={handleDropdownItemClick}
          />

          <CompanySubNav
            isOpen={openDropdown === "company"}
            onMouseEnter={() => handleMouseEnterDropdown("company")}
            onMouseLeave={closeAllDropdowns}
            onItemClick={handleDropdownItemClick}
          />

          {navLinks.map((link) => (
            <Link
              key={link.to}
              to={link.to}
              className=" text-[#F5F5F7] hover:text-white transition"
              onClick={closeAllDropdowns}
            >
              {link.label}
            </Link>
          ))}
        </nav>
        <div className="flex justify-center items-center xl:gap-6 gap-4">
          <LanguageSubNav />

          {isAuthenticated ? (
            <div className="hidden md:flex items-center space-x-4">
              <Link to={user?.role === "ADMIN" ? "/admin" : "/dashboard"}>
                <Avatar>
                  <AvatarImage src={UPLOAD_URL + user?.profile?.avatarUrl} />
                  <AvatarFallback className="text-xl font-bold">
                    {user?.profile?.firstName?.charAt(0).toUpperCase() || ""}
                    {user?.profile?.lastName?.charAt(0).toUpperCase() || ""}
                  </AvatarFallback>
                </Avatar>
              </Link>
            </div>
          ) : (
            <div className="flex items-center xl:gap-6 gap-4 justify-center">
              <div className="hidden lg:flex items-center space-x-4">
                <Button
                  outline={false}
                  variant={"animeShine"}
                  className="rounded-full bg-black px-10 hover:bg-black lg:text-base text-sm font-normal text-[#F5F5F7] h-11"
                  onClick={() => setIsShowLoginModal(true)}
                >
                  Sign in
                </Button>
                <Button
                  outline={false}
                  variant={"animeGradient"}
                  className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 px-10 lg:text-base text-sm font-normal text-[#F5F5F7] h-11 py-[6px]"
                  onClick={() => setIsShowSignUpModal(true)}
                >
                  Sign up
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
