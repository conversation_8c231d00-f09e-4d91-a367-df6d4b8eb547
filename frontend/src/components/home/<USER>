import { Text } from "@/components/ui/text";
import { useState } from "react";
import { TabSelect } from "../ui/tab-select";
import VirtualTryOn from "./VirtualTryOn";
import SpeedPainting from "./Speedpainting";
import BackgroundRemover from "./BackgroundRemover/BackgroundRemover";

const Tabs = [
  "Background Remover",
  "Speed Painting",
  "Virtual Try On",
  "Image Generator",
  "Sales Pilot",
  "Face Swap",
] as const;

const VisibilityAI = () => {
  const [activeTool, setActiveTool] =
    useState<(typeof Tabs)[number]>("Background Remover");
  return (
    <div className="min-h-screen relative w-full">
      <div className="lg:space-y-16 space-y-10 px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
        <Text
          variant={"body"}
          className="text-[#f5f5f7] text-4xl lg:text-5xl font-semibold leading-tight text-center lg:max-w-[820px] w-full mx-auto"
        >
          Boost your site's visibility with AI-powered Miragic tools
        </Text>

        <div className="flex flex-wrap items-center justify-center w-full lg:max-w-[820px] mx-auto">
          {Tabs.map((tool) => (
            <TabSelect
              key={tool}
              name={tool}
              isActive={tool === activeTool}
              onClick={() => setActiveTool(tool)}
              className="cursor-pointer"
              activeClassName="text-[#2C9BF7] relative before:absolute before:bottom-0 before:left-0 before:w-full before:h-[2px] before:bg-[#2C9BF7] before:origin-left before:scale-x-100 before:transition-transform before:duration-500"
              inactiveClassName="text-[#f5f5f7] border-b-2 border-[#f5f5f7] relative before:absolute before:bottom-0 before:left-0 before:w-full before:h-[2px] before:bg-[#2C9BF7] before:origin-right before:scale-x-0 before:transition-transform before:duration-500"
            />
          ))}
        </div>

        <div className="flex justify-center items-center w-full">
          {activeTool === "Background Remover" ? (
            <BackgroundRemover />
          ) : activeTool === "Virtual Try On" ? (
            <VirtualTryOn />
          ) : activeTool === "Speed Painting" ? (
            <SpeedPainting />
          ) : (
            <BackgroundRemover />
          )}
        </div>
      </div>
      
      <div className="absolute top-20 -left-[5%] w-[320px] h-[320px] ">
        <img src="/icons/left_elipse.svg" alt="Background Remover" className="w-full h-full object-cover rounded-full" />
      </div>

    </div>
  );
};

export default VisibilityAI;
