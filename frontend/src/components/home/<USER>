// No imports needed

import GradientText from "../common/GradientText";
import { Text } from "../ui/text";

const TrustedTeam = () => {
  const TRUSTED_TEAM = [
    "/png/calestial.png",
    "/png/apex.png",
    "/png/quantum.png",
    "/png/acme_corp.png",
    "/png/pulsew.png",
  ];
  return (
    <div className="flex flex-col gap-10 justify-center w-full items-center">
      <Text className="text-white no-break font-inter xl:text-2xl text-xl font-light tracking-wide">
        <GradientText>150M+</GradientText>{" "}
        Asset Creations Trusted by Fortune{" "}
        <GradientText>500</GradientText>{" "}
        Companies
      </Text>
      <div className="flex flex-wrap xl:gap-16 gap-10 justify-center">
        {TRUSTED_TEAM?.map((src) => (
          <img 
            className="max-h-10 max-w-fit opacity-80 hover:opacity-100 transition-opacity duration-300" 
            key={src} 
            src={src} 
            alt="Trusted company logo"
          />
        ))}
      </div>
    </div>
  );
};

export default TrustedTeam;
