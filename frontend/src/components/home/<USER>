import TrustedTeam from "./TrustedTeam";
import { Link, useNavigate } from "react-router-dom";
import { useApp } from "@/contexts/useApp";
import { GradientBackground } from "../common/GradientBackground";
import VideoPlayer from "../common/VideoPlayer";
import { Text } from "@/components/ui/text";
import { Button } from "@/components/ui/button";
import { FaYoutube } from "react-icons/fa";

interface HeroSectionProps {
  setIsShowSignUpModal: (isShow: boolean) => void;
}

const HeroSection = ({ setIsShowSignUpModal }: HeroSectionProps) => {
  const { isAuthenticated } = useApp();

  const navigate = useNavigate();

  return (
    <div className="relative">
      <GradientBackground
        imageUrl="/bg/price_bg.svg"
        startColor="rgba(18, 24, 40, 0.7)"
        endColor="rgba(18, 24, 40, 0.1)"
        direction="to bottom"
        className="bottom-[10%]"
      />
      <section className="relative w-full overflow-hidden px-4 sm:px-6 lg:px-8 2xl:px-[120px] max-w-[1440px] mx-auto">
        <div className="mt-10 text-white grid lg:grid-cols-2 gap-8 items-center">
          <div className="space-y-8">
            <div className="border border-[#8054F3] rounded-full inline-flex items-center shadow-border-glow backdrop-blur-md">
              <Text className="text-sm text-[#f5f5f7] uppercase tracking-widest px-4 py-[6px]">
                Latest integration just arrived
              </Text>
            </div>
            <Text
              variant={"body"}
              className="text-[#f5f5f7] text-4xl lg:text-5xl xl:text-6xl font-semibold leading-tight"
            >
              Design The Future <br className="lg:block hidden" />
              with Miragic AI
            </Text>
            <Text className="text-[#888] text-xl lg:text-2xl">
              No Code, No Design, No Problem
            </Text>
            <div className="flex gap-4">
              <Button
                outline={false}
                onClick={() =>
                  isAuthenticated
                    ? navigate("/signup")
                    : setIsShowSignUpModal(true)
                }
                variant={"animeGradient"}
                className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 px-7 h-[50px]"
              >
                Get Started
              </Button>
              <Button
                outline={false}
                onClick={() => navigate("/docs-api")}
                variant={"animeShine"}
                className="rounded-full bg-black px-7 hover:bg-black h-[50px]"
              >
                API Docs
              </Button>
            </div>
          </div>
          <div className="relative w-full overflow-hidden shadow-[0_0_20px_rgba(129,90,219,0.3)] group">
            <VideoPlayer src="/video/hero_section.mp4" ariaLabel="Product demo video" />
            <Link
              to="https://www.youtube.com/watch?v=Ein9w0akpvc"
              target="_blank"
              rel="noopener noreferrer"
              className="absolute top-3 right-3 flex items-center gap-2 px-3 py-1.5 bg-black/80 text-white rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"
            >
              <FaYoutube className="w-5 h-5 text-red-500" />
              <span className="font-medium text-xs">Watch on YouTube</span>
            </Link>
          </div>
        </div>
        <div className="pb-20 mt-36 ">
          <TrustedTeam />
        </div>
      </section>
    </div>
  );
};

export default HeroSection;
