import { useNavigate } from "react-router-dom";
import { Banner } from "../common/Banner";
import { Text } from "../ui/text";

const BannerImages1st = [
  {
    src: "/icons/banner/banner-1.svg",
    alt: "Floating Image 1",
    className: "absolute -top-[12%] left-[30%] w-[68px] h-[86px]",
  },
  {
    src: "/icons/banner/banner-2.svg",
    alt: "Floating Image 2",
    className: "absolute top-1/5 left-[10%] w-[100px] h-[125px]",
  },
  {
    src: "/icons/banner/banner-3.svg",
    alt: "Floating Image 3",
    className: "absolute -bottom-[18%] left-[25%] w-[100px] h-[125px]",
  },
  {
    src: "/icons/banner/banner-4.svg",
    alt: "Floating Image 4",
    className: "absolute right-0 -top-[10%] w-[142px] h-[178px] z-10",
  },

  {
    src: "/icons/banner/banner-7.svg",
    alt: "Floating Image 7",
    className: "absolute left-0 top-1/2 -translate-y-1/2 w-[68px] h-[86pxpx]",
  },
  {
    src: "/icons/banner/banner-1.svg",
    alt: "Floating Image 1",
    className: "absolute -bottom-[22%] left-[45%] w-[132px] h-[168px] z-10",
  },
  {
    src: "/icons/banner/banner-5.svg",
    alt: "Floating Image 5",
    className: "absolute -bottom-[32%] -right-[3%] w-[100px] h-[125px]",
  },
  {
    src: "/icons/banner/banner-6.svg",
    alt: "Floating Image 6",
    className: "absolute -top-[10%] right-[12%] w-[100px] h-[125px] z-0",
  },
  {
    src: "/icons/banner/banner-8.svg",
    alt: "Floating Image 8",
    className: "absolute top-[10%] right-[25%] w-[100px] h-[125px] z-0",
  },
];

const BannerImages2nd = [
    {
        src: "/icons/banner/discover_icon.svg",
        alt: "Floating Image 1",
        className: " object-cover w-full h-full min-h-[308px]",
    },
]

export default function BannerSection() {
  const navigate = useNavigate();
  return (
    <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
      <div>
        <Banner
          title="Free Online Tools"
          subtitle="Content generation for your marketing"
          leftContentClassName="lg:w-1/2 w-full"
          rightContentClassName="lg:w-1/2 w-full  min-h-[232px]"
          buttonText="Explore community creations"
          onClick={() => {
           navigate("/community")
          }}
          floatingImages={BannerImages1st}
        />
        <div className="my-14 flex justify-center gap-16">
          <div className="flex flex-col items-center gap-4 z-20">
            <Text className="xl:text-7xl text-5xl font-semibold text-white font-inter">1.2M</Text>
            <Text className="text-base text-white/65 font-inter">
              Join Miragic users
            </Text>
          </div>
          <div className="flex flex-col items-center gap-4 z-20">
            <Text className="xl:text-7xl text-5xl font-semibold text-white font-inter">20K</Text>
            <Text className="text-base text-white/65 font-inter">Companies</Text>
          </div>
        </div>
        <Banner
          title="Discover consumer products"
          bannerClassName="bg-[#0B1016]"
          leftContentClassName="lg:w-[55%] w-full"
          rightContentClassName="lg:w-[45%] w-full h-full"
          subtitle="Free Image Background Removal, Virtual Try On, Speedpainting in One Platform"
          buttonText="Learn more"
          onClick={() => {
            navigate("/dashboard")
          }}
          floatingImages={BannerImages2nd}
        />
      </div>
    </div>
  );
}
