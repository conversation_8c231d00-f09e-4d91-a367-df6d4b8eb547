import { Star } from "lucide-react";
import { Text } from "../ui/text";
import GradientText from "../common/GradientText";
import { SeperatorLineIcon } from "@/lib/icons";

const REVIEWS = [
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_1.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "<PERSON>gic’s image generator is amazing for creating promotional visuals. The results are high-quality and require almost no touch-up.",
  },
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_2.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Our sales team swears by Miragic’s Sales Pilot tool. It provides smart lead insights and has noticeably improved our conversion rate.",
  },
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_3.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "I’ve tried several background removal tools, but Miragic is by far the fastest and most precise. Great for batch processing too!",
  },
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_4.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The virtual-try-on tool is helping our beauty brand reduce returns. Customers get a true sense of how a shade or product looks on them.",
  },
  {
    name: "<PERSON><PERSON>",
    avatar: "/icons/user_5.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Miragic’s image generator is amazing for creating promotional visuals. The results are high-quality and require almost no touch-up.",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_6.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Our sales team swears by Miragic’s Sales Pilot tool. It provides smart lead insights and has noticeably improved our conversion rate.",
  },
  {
    name: "Ashlynn Philips",
    avatar: "/icons/user_7.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "I’ve tried several background removal tools, but Miragic is by far the fastest and most precise. Great for batch processing too!",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_1.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The virtual-try-on tool is helping our beauty brand reduce returns. Customers get a true sense of how a shade or product looks on them.",
  },
  {
    name: "Ashlynn Philips",
    avatar: "/icons/user_2.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Miragic’s image generator is amazing for creating promotional visuals. The results are high-quality and require almost no touch-up.",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_3.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "Our sales team swears by Miragic’s Sales Pilot tool. It provides smart lead insights and has noticeably improved our conversion rate.",
  },
  {
    name: "Ashlynn Philips",
    avatar: "/icons/user_4.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "I’ve tried several background removal tools, but Miragic is by far the fastest and most precise. Great for batch processing too!",
  },
  {
    name: "Zain Philips",
    avatar: "/icons/user_5.svg",
    date: "Sep 27, 2025",
    stars: 5,
    text: "The virtual-try-on tool is helping our beauty brand reduce returns. Customers get a true sense of how a shade or product looks on them.",
  },
  // ...add more reviews as needed
];

export default function CustomerReview() {
  return (
    <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
      <div className="mb-16 mx-auto">
        <SeperatorLineIcon />
      </div>
      <Text className="text-white text-lg md:text-2xl font-medium text-center xl:leading-10 mb-16 xl:mb-24">
        Miragic is becoming an industry standard for marketing and advertising
        creators and innovators with <GradientText>millions</GradientText> of
        assets generated
      </Text>
      <div
        className="mx-auto px-2 relative"
        style={{
          maskImage: `linear-gradient(to bottom, 
            rgba(18, 24, 40, 0.1) 0%, 
            rgba(18, 24, 40, 0.7) 20%, 
            rgba(18, 24, 40, 0.7) 80%, 
            rgba(18, 24, 40, 0.1) 100%)`,
          WebkitMaskImage: `linear-gradient(to bottom, 
            rgba(18, 24, 40, 0.1) 0%, 
            rgba(18, 24, 40, 0.7) 20%, 
            rgba(18, 24, 40, 0.7) 80%, 
            rgba(18, 24, 40, 0.1) 100%)`,
        }}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-7 relative">
          {REVIEWS.map((r, idx) => (
            <div
              key={idx}
              className={
                `bg-gradient-to-br from-[#23243a] to-[#1a1b2b] border border-white/10 rounded-2xl p-6 flex flex-col min-h-[200px] shadow-lg transition-transform ` +
                (idx % 2 === 0 ? "md:-translate-y-14" : "md:translate-y-14")
              }
              style={{ transition: "transform 0.3s" }}
            >
              <div className="flex items-center gap-3 mb-3">
                <img
                  src={r.avatar}
                  alt={r.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white/15"
                />
                <div>
                  <Text className="font-semibold text-white text-base">
                    {r.name}
                  </Text>
                </div>
              </div>
              <div className="flex items-center mb-2">
                {[...Array(r.stars)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 text-[#FFD600] fill-[#FFD600] mr-1"
                  />
                ))}
              </div>
              <Text className="text-white/90 text-base">{r.text}</Text>
              <Text className="text-xs text-white/60 mt-6">{r.date}</Text>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
