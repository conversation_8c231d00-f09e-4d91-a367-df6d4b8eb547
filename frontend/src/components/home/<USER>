import { SeperatorLineIcon } from "@/lib/icons";
import { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { Swiper as SwiperType } from "swiper";
import { ChevronLeft, ChevronRight } from "lucide-react";
import "swiper/css";
import BlogService, {
  type ApiBlogPost,
  type BlogPost,
} from "@/services/blog.service";
import "swiper/css/navigation";
import { Text } from "../ui/text";

interface PaginatedApiBlogPosts {
  posts: ApiBlogPost[];
  totalCount?: number;
  totalPages?: number;
  currentPage?: number;
}

export default function RelatedArticle() {
  const swiperRef = useRef<SwiperType | null>(null);

  const [blogData, setBlogData] = useState<BlogPost[]>([]);

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        const response = (await BlogService.getPosts({
          limit: 10,
        })) as unknown as PaginatedApiBlogPosts;

        const transformedData: BlogPost[] = response.posts.map(
          (post: ApiBlogPost) => ({
            id: post.id,
            title: post.title,
            slug: post.slug,
            content: post.content || "",
            excerpt: post.excerpt || "",
            status: post.status || "PUBLISHED",
            authorId: post.authorId || post.author.id,
            categoryIds: post.categories.map((cat) => cat.id),
            tagIds: post.tags.map((tag) => tag.id),
            publishedAt: post.publishedAt || undefined,
            createdAt: post.createdAt,
            updatedAt: post.updatedAt,
            featuredImageUrl: post.featuredImageUrl || undefined,
            // Store the full objects for display purposes
            categories: post.categories,
            tags: post.tags,
            author: post.author,
          })
        );

        setBlogData(transformedData);
      } catch (error) {
        console.error("Error fetching blog posts:", error);
      } finally {
        // Your finally block code here
      }
    };

    fetchBlogPosts();
  }, []);
  return (
    <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
      <div className="mb-16 mx-auto">
        <SeperatorLineIcon />
      </div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-white text-3xl font-semibold">Related Articles</h2>
        <div className="flex gap-2">
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="rounded-full w-10 h-10 text-white flex items-center justify-center text-xl hover:bg-white/25 bg-white/10 transition"
          >
            <ChevronLeft />
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="rounded-full w-10 h-10 text-white flex items-center justify-center text-xl hover:bg-white/25 bg-white/10 transition"
          >
            <ChevronRight />
          </button>
        </div>
      </div>
      <Swiper
        modules={[Navigation]}
        onSwiper={(swiper: SwiperType) => {
          swiperRef.current = swiper;
        }}
        spaceBetween={24}
        slidesPerView={1}
        breakpoints={{
          640: { slidesPerView: 1.2 },
          768: { slidesPerView: 2 },
          1024: { slidesPerView: 3 },
        }}
        className="pb-8"
      >
        {blogData.map((post) => (
          <SwiperSlide key={post.id} className="h-auto">
            <div className="bg-white/5 border border-white/20 rounded-2xl p-4 flex flex-col h-full min-h-[340px] max-w-[370px] mx-auto">
              {post.featuredImageUrl && (
                <img
                  src={post.featuredImageUrl}
                  alt={post.title}
                  className="w-full h-[140px] object-cover rounded-lg mb-4"
                />
              )}
              <Text className="text-xs text-gray-400 mb-2">
                {new Date(
                  post.publishedAt || post.createdAt || ""
                ).toLocaleDateString("en-GB", {
                  day: "2-digit",
                  month: "long",
                  year: "numeric",
                })}
              </Text>
              <Text className="font-semibold text-white text-base mb-2 line-clamp-2">
                {post.title}
              </Text>
              <Text className="text-gray-400 text-sm line-clamp-3 mb-2">
                {post.excerpt || post.content}
              </Text>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
}
