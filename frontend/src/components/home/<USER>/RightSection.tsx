import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { AlertCircle, Plus, RefreshCw, X } from "lucide-react";
import type { FileHandlerState } from "../../../hooks/useFileHandler";
import type { DragEvent } from "react";

// Constants
const MODEL_IMAGES = Array.from(
  { length: 10 },
  (_, i) => `/Human_model/${i + 1}.jpg`
);
const ACCEPTED_FILE_TYPES = [
  "image/png",
  "image/jpeg",
  "image/jpg",
  "image/webp",
];

interface RightSectionProps {
  fileHandler: {
    state: FileHandlerState;
    resetState: () => void;
    handleFile: (file: File) => void;
    handleModelSelect: (modelPath: string) => void;
    handleDownload: () => Promise<void>;
  };
}

const ErrorDisplay = ({ message }: { message: string }) => (
  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-11/12">
    <div className="p-3 bg-red-900/30 border border-red-700 rounded-lg flex items-center gap-2">
      <AlertCircle className="text-red-500" size={18} />
      <p className="text-red-400 text-sm">{message}</p>
    </div>
  </div>
);

const ModelPreview = ({
  modelPath,
  isSelected,
  onSelect,
}: {
  modelPath: string;
  isSelected: boolean;
  onSelect: () => void;
}) => (
  <div
    onClick={onSelect}
    className={`relative aspect-square rounded-lg overflow-hidden border-2 cursor-pointer transition-all hover:scale-105 ${
      isSelected
        ? "border-purple-500 shadow-[0_0_10px_rgba(129,90,219,0.6)]"
        : "border-gray-500 hover:border-purple-400"
    }`}
  >
    <img
      src={modelPath}
      alt={`Model ${modelPath}`}
      className="w-full h-full object-cover"
      onError={(e) => {
        e.currentTarget.src =
          "https://via.placeholder.com/100x100/333333/ffffff?text=Model";
      }}
    />
    {isSelected && (
      <div className="absolute inset-0 bg-purple-500/20 flex items-center justify-center">
        <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
          <svg
            className="w-4 h-4 text-white"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </div>
      </div>
    )}
  </div>
);

export const RightSection = ({ fileHandler }: RightSectionProps) => {
  const { state, resetState, handleFile, handleModelSelect, handleDownload } =
    fileHandler;
  const { previewUrl, uploadedFile, processedImage, error, selectedModel } =
    state;

  const hasFile = !!uploadedFile || !!selectedModel;

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files?.[0]) handleFile(files[0]);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files?.[0]) handleFile(files[0]);
  };

  return (
    <div className="flex flex-col gap-6 lg:w-1/2 w-full">
      <div
        className={`border border-white/15 rounded-lg bg-gradient-to-r from-[rgba(44,155,247,0.10)] to-[rgba(128,84,243,0.10)] flex items-center justify-center w-full min-h-[300px] max-h-[400px] relative ${
          hasFile ? "" : "p-8"
        }`}
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id="file-upload"
          accept={ACCEPTED_FILE_TYPES.join(",")}
          onChange={handleFileSelect}
          className="hidden"
        />

        <label
          htmlFor="file-upload"
          className="cursor-pointer relative w-full h-full flex items-center justify-center"
        >
          {(uploadedFile || selectedModel) && previewUrl ? (
            <div className="relative w-full h-full">
              <Button
                onClick={resetState}
                variant="animeGradient"
                className="border-none absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full cursor-pointer hover:bg-red-600 transition z-10"
              >
                <X className="w-4 h-4" />
              </Button>
              <img
                src={previewUrl}
                alt="Selected preview"
                className="w-full h-full max-h-[350px] object-contain rounded-lg"
              />
              {processedImage && (
                <div className="absolute px-4 lg:px-5 min-h-16 lg:min-h-20 flex items-center justify-center gap-3 lg:gap-5 transform -translate-y-3 lg:-translate-y-4 -translate-x-1/2 bottom-0 left-1/2 rounded-[10px] border border-[rgba(255,255,255,0.2)] bg-[rgba(14,14,20,0.83)] backdrop-blur-[10.9px]">
                  <Button
                    variant="animeShine"
                    onClick={resetState}
                    className="z-20 flex items-center gap-4 border border-gray-500 rounded-md px-3 lg:px-4 py-2 hover:bg-gray-900 cursor-pointer"
                  >
                    <RefreshCw className="text-gray-400 w-4 h-4" />
                  </Button>
                  <Button
                    variant="animeGradient"
                    onClick={handleDownload}
                    className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[50px]"
                  >
                    Download
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full space-y-5">
              <Button
                variant="animeGradient"
                onClick={() => document.getElementById("file-upload")?.click()}
                className="bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] rounded-full border border-white/15 !px-7 h-[50px]"
              >
                <Plus />
                <span>Upload a file</span>
              </Button>
              <Text className="text-xl text-white max-w-[300px] text-center">
                or drag an image
              </Text>
              <Text className="text-sm text-gray-500 max-w-[300px] text-center">
                Original image must be .png, .jpg, .jpeg or webp format and 30mb
                max size.
              </Text>
            </div>
          )}
        </label>

        {error && <ErrorDisplay message={error} />}
      </div>

      <div className="flex gap-4 items-center">
        <div className="text-center lg:text-left">
          <Text className="text-base text-white/65 mb-2">
            No Image <br />
            Try one of these:
          </Text>
        </div>

        <div className="grid grid-cols-5 gap-3 max-w-[420px] mx-auto lg:mx-0">
          {MODEL_IMAGES.slice(0, 5).map((modelPath, index) => (
            <ModelPreview
              key={index}
              modelPath={modelPath}
              isSelected={selectedModel === modelPath}
              onSelect={() => handleModelSelect(modelPath)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
