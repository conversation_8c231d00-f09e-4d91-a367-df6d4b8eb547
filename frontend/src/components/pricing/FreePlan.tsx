import { Text } from "../ui/text";
import { Button } from "../ui/button";
import type { User } from "@/services/auth.service";

interface FreePlanFeature {
  icon: string;
  text: string;
}

interface FreePlanProps {
  user: User | null;
}

const freePlanFeatures: FreePlanFeature[] = [
  {
    icon: "/icons/album.svg",
    text: "20 Free credits",
  },
  {
    icon: "/icons/unlimited.svg",
    text: "Unlimited background removal try",
  },
  {
    icon: "/icons/setting.svg",
    text: "20 Virtual Try on Clothes",
  },
  {
    icon: "/icons/ps.svg",
    text: "4 Speed Painting Videos",
  },
  {
    icon: "/icons/refer.svg",
    text: "Refer friends and earn free credits",
  },
];

export const FreePlan = (user: FreePlanProps) => {
  return (
    <>
      <div className="p-5 py-10 flex flex-col gap-8 ">
        <Text variant={"card_title"} className="text-[#F5F5F7] text-center">
          Free plan
        </Text>
        <div className="flex flex-col gap-[36px] px-8 pb-16">
          {freePlanFeatures.map((feature, index) => (
            <div key={index} className="flex items-center gap-4">
              <img src={feature.icon} className="w-8 h-auto rounded-full" />
              <Text
                variant="card_list_title"
                className="text-[#D9D9D9] font-light"
              >
                {feature.text}
              </Text>
            </div>
          ))}
        </div>
        <Button
          outline={false}
          variant={"animeShine"}
          className="w-full px-5 border border-white/15 rounded-full bg-[#192f50] h-[50px]"
          disabled={!user}
          onClick={() => (window.location.href = "/auth/register")}
          maxShineDelayMs={1500}
        >
          Sign up for free
        </Button>
      </div>
      <div className="flex py-5 px-20 flex-col gap-2 items-center justify-center bg-[#192f50]">
        <Text
          variant={"section_title"}
          className="text-white font-light text-[12px] text-center"
        >
          <span className="font-semibold">Free Forever: </span>
          Recommended for personal use and evaluation
        </Text>
      </div>
    </>
  );
};
