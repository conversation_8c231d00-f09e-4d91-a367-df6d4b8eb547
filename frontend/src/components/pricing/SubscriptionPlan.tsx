import { Text } from "../ui/text";
import { But<PERSON> } from "../ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import RadioButton from "@/components/ui/radioBtn";
import type {
  UserSubscription,
  SubscriptionPlan as ISubscriptionPlan,
} from "@/services/subscription.service";
import GradientText from "../common/GradientText";

interface SubscriptionPlanProps {
  plansLoading: boolean;
  plansError: string | null;
  subscriptionPlans: ISubscriptionPlan[];
  selectedSubscriptionId: string;
  setSelectedSubscriptionId: (id: string) => void;
  userSubscription: UserSubscription | null;
  subscriptionLoading: boolean;
  handleSubscribe: () => void;
  handleOpenCancellationModal: () => void;
  handleZIndex: (
    cardType: "free" | "subscription" | "topup",
    id: string
  ) => void;
}

export const SubscriptionPlan = ({
  plansLoading,
  plansError,
  subscriptionPlans,
  selectedSubscriptionId,
  setSelectedSubscriptionId,
  userSubscription,
  subscriptionLoading,
  handleSubscribe,
  handleOpenCancellationModal,
  handleZIndex,
}: SubscriptionPlanProps) => {
  return (
    <>
      <div className="absolute -right-2 -top-2">
        <div className="relative">
          <img
            src="/icons/banner.svg"
            className="w-full h-full"
            alt="Banner Icon"
          />
          <p className="absolute top-12 right-5 rotate-48 text-center text-black text-sm font-semibold whitespace-nowrap">
            Best Value
          </p>
        </div>
      </div>
      <div className="p-4 pt-10 flex flex-col gap-8 ">
        <Text variant={"card_title"} className="text-[#F5F5F7] text-center">
          Subscription Plan
        </Text>
        {plansLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
        ) : plansError ? (
          <div className="text-red-500 py-4 text-center">{plansError}</div>
        ) : (
          <>
            <Text variant={"section_title"} className="text-center">
              <GradientText className="text-5xl font-semibold">
                {selectedSubscriptionId && subscriptionPlans.length > 0 ? (
                  <>
                    {subscriptionPlans.find(
                      (plan) => plan.id === selectedSubscriptionId
                    )?.currency === "USD" && "$"}
                    {subscriptionPlans.find(
                      (plan) => plan.id === selectedSubscriptionId
                    )?.price || 0}
                  </>
                ) : (
                  "$0"
                )}
              </GradientText>{" "}
              <GradientText className="text-[16px]">
                /{" "}
                {(selectedSubscriptionId &&
                  subscriptionPlans.find(
                    (plan) => plan.id === selectedSubscriptionId
                  )?.interval) ||
                  "month"}
              </GradientText>
            </Text>
            <div className="flex flex-col gap-2">
              {subscriptionPlans.map((plan) => {
                // Calculate price per credit (if features include credits)
                const creditsPerMonth =
                  plan.features?.credits ||
                  plan.features?.creditsPerMonth ||
                  (typeof plan.features === "object" &&
                  "credits" in plan.features
                    ? plan.features.credits
                    : 0);
                const pricePerCredit = creditsPerMonth
                  ? (plan.price / Number(creditsPerMonth)).toFixed(3)
                  : "N/A";

                return (
                  <div
                    key={plan.id}
                    className={cn(
                      "w-full grid cursor-pointer p-2.5 grid-cols-12 gap-4",
                      selectedSubscriptionId === plan.id
                        ? "bg-[#192f50]"
                        : userSubscription?.plan.id === plan.id
                        ? "bg-sky-400 "
                        : ""
                    )}
                    onClick={() => handleZIndex("subscription", plan.id)}
                  >
                    <div className="col-span-7">
                      <RadioButton
                        name="subscription"
                        value={plan.id}
                        checked={selectedSubscriptionId === plan.id}
                        onChange={(value) => setSelectedSubscriptionId(value)}
                        label={`${creditsPerMonth} credits/month`}
                        labelClass="text-[#D9D9D9] text-base"
                      />
                    </div>
                    <Text
                      variant={"card_body"}
                      className="col-span-2 font-light text-[#D9D9D9] text-base"
                    >
                      {plan.currency === "USD" && "$ "}
                      {plan.price}
                    </Text>
                    <Text
                      variant={"card_body"}
                      className="text-[#D9D9D9] text-base text-right col-span-3 font-light"
                    >
                      {plan.currency === "USD" && "$"}
                      {pricePerCredit}/credit
                    </Text>
                  </div>
                );
              })}
            </div>
          </>
        )}

        <div className="flex flex-col gap-2.5 justify-center items-center">
          {userSubscription && userSubscription.status === "ACTIVE" ? (
            <div className="flex flex-col gap-2 w-full">
              <div className="p-3 bg-green-900/20 border border-green-700/30 rounded-md mb-2">
                <Text
                  variant={"card_body"}
                  className="text-white font-medium mb-1"
                >
                  Current Plan: {userSubscription.plan.displayName}
                </Text>
                <Text variant={"card_body"} className="text-white/70 text-sm">
                  Renews on:{" "}
                  {new Date(
                    userSubscription.currentPeriodEnd
                  ).toLocaleDateString()}
                </Text>
              </div>
              <Button
                outline={false}
                variant={"animeShine"}
                className="w-full bg-green-600 hover:bg-green-700 h-[50px]"
                disabled={true}
                maxShineDelayMs={1500}
              >
                <span className="flex items-center">
                  <span className="mr-2 h-2 w-2 rounded-full bg-green-400"></span>
                  Currently Subscribed
                </span>
              </Button>
              {!!userSubscription?.endDate &&
              new Date(userSubscription.endDate) > new Date() ? (
                <Button
                  outline={true}
                  variant={"outline"}
                  className="w-full"
                  disabled={true}
                >
                  Already Cancelled
                </Button>
              ) : (
                <Button
                  outline={true}
                  variant={"animeShine"}
                  className="w-full h-[50px]"
                  onClick={handleOpenCancellationModal}
                  disabled={subscriptionLoading}
                  maxShineDelayMs={1500}
                >
                  Cancel Subscription
                </Button>
              )}
            </div>
          ) : userSubscription && userSubscription.status === "CANCELED" ? (
            <div className="flex flex-col gap-2 w-full">
              <div className="p-3 bg-amber-900/20 border border-amber-700/30 rounded-md mb-2">
                <Text
                  variant={"card_body"}
                  className="text-white font-medium mb-1"
                >
                  Plan: {userSubscription.plan.displayName}
                </Text>
                <Text variant={"card_body"} className="text-white/70 text-sm">
                  Access until:{" "}
                  {new Date(
                    userSubscription.currentPeriodEnd
                  ).toLocaleDateString()}
                </Text>
              </div>
              <Button
                outline={false}
                variant={"animeShine"}
                className="w-full bg-amber-600 hover:bg-amber-700 h-[50px]"
                disabled={true}
                maxShineDelayMs={1500}
              >
                <span className="flex items-center">
                  <span className="mr-2 h-2 w-2 rounded-full bg-amber-400"></span>
                  Subscription Ending
                </span>
              </Button>
              {/* Only show resubscribe button if the current period end date has passed */}
              {new Date(userSubscription.currentPeriodEnd) < new Date() && (
                <Button
                  outline={false}
                  variant={"animeShine"}
                  className="w-full h-[50px]"
                  onClick={handleSubscribe}
                  disabled={subscriptionLoading || !selectedSubscriptionId}
                  maxShineDelayMs={1500}
                >
                  {subscriptionLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Resubscribe"
                  )}
                </Button>
              )}
            </div>
          ) : (
            <Button
              outline={false}
              variant={"animeGradient"}
              className="w-full rounded-full bg-radial-[at_5%_5%] from-[#2C9BF7] to-[#8054F3] h-[50px]"
              onClick={handleSubscribe}
              disabled={subscriptionLoading || !selectedSubscriptionId}
              maxShineDelayMs={1500}
            >
              {subscriptionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Subscribe now"
              )}
            </Button>
          )}{" "}
          <Text variant={"section_title"} className=" font-light text-[12px]">
            *Price excluding VAT, if applicable
          </Text>
        </div>
      </div>
      <div className="flex p-5 flex-col gap-2 items-center justify-center bg-[#192f50]">
        <div className="flex items-center gap-2 text-center">
          <Text
            variant={"section_title"}
            className="text-white font-light text-[12px]"
          >
            <span className="font-semibold">Risk free: </span>
            14 Days Money Back Guarantee
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Text
            variant={"section_title"}
            className="text-white font-light text-[12px]"
          >
            <span className="font-semibold">Flexible: </span>
            Downgrade, upgrade or cancel any time
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Text
            variant={"section_title"}
            className="text-white font-light text-[12px]"
          >
            <span className="font-semibold">Fair: </span>
            Unused credits roll over as long as you're subscribed
          </Text>
        </div>
      </div>
    </>
  );
};
