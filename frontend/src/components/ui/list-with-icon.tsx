import { Text } from "./text";

interface ListWithIconProps {
  text: string;
  icon: string;
  isActive: boolean;
  onClick: () => void;
}

export const ListWithIcon: React.FC<ListWithIconProps> = ({
  text,
  icon,
  isActive,
  onClick,
}) => {
  const backgroundColor = isActive ? "bg-[#212133]" : "hover:bg-[#1a1a2e]";

  return (
    <button
      className={`
          flex items-center gap-4 p-3 rounded-xl cursor-pointer
          ${backgroundColor}
          transition-colors duration-200 ease-in-out min-w-[164px]
        `}
      onClick={onClick}
    >
      <img
        src={icon}
        alt={`${text} icon`}
        className="w-8 h-8 rounded-full border border-gray-600 object-cover"
        onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
          const target = e.target as HTMLImageElement;
          target.onerror = null;
          target.src = "https://placehold.co/30x30/4A5568/FFFFFF?text=N/A";
        }}
      />
      <Text className="text-white text-sm font-medium">{text}</Text>
    </button>
  );
};
