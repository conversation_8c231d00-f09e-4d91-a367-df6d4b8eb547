import { Text } from "@/components/ui/text";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface ToolItemProps {
  name: string;
  isActive?: boolean;
  onClick?: () => void;
}

const ToolItem = ({ name, isActive, onClick }: ToolItemProps) => (
  <button
    onClick={onClick}
    className={cn(
      "px-4 py-2 text-sm transition-all duration-300",
      "hover:text-white/90 hover:bg-white/5 rounded-full",
      isActive ? "text-white border-b-2 border-[#2C9BF7]" : "text-white/70"
    )}
  >
    {name}
  </button>
);

interface UploadAreaProps {
  title: string;
  description: string;
  acceptedFormats: string;
  sampleImages: string[];
  onUpload: (file: File) => void;
}

const UploadArea = ({
  /*title, description,*/ acceptedFormats,
  sampleImages,
  onUpload,
}: UploadAreaProps) => {
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) onUpload(file);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) onUpload(file);
  };

  return (
    <div className="mt-6 w-full max-w-2xl">
      <div
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
        className="relative border border-white/10 rounded-xl p-8 bg-white/5 backdrop-blur-sm"
      >
        <div className="flex flex-col items-center justify-center gap-4 min-h-[300px]">
          <button
            onClick={() => document.getElementById("fileInput")?.click()}
            className="px-6 py-3 bg-gradient-to-r from-[#2C9BF7] to-[#8054F3] rounded-full text-white hover:opacity-90 transition-opacity"
          >
            Upload a file
          </button>
          <input
            id="fileInput"
            type="file"
            className="hidden"
            onChange={handleFileInput}
            accept={acceptedFormats}
          />
          <Text className="text-white/60 text-sm text-center">
            or drag a image
          </Text>
          <Text className="text-white/40 text-xs text-center">
            {acceptedFormats} format and 30mb max size.
          </Text>
        </div>
      </div>

      <div className="mt-6">
        <Text className="text-white/60 text-sm mb-4">
          No image? Try one of these:
        </Text>
        <div className="flex gap-4 overflow-x-auto pb-2">
          {sampleImages.map((src, index) => (
            <img
              key={index}
              src={src}
              alt={`Sample ${index + 1}`}
              className="w-20 h-20 rounded-lg object-cover cursor-pointer hover:ring-2 ring-[#2C9BF7] transition-all"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const TOOLS = [
  "Background Remover",
  "Speed Painting",
  "Virtual Try On",
  "Image Generator",
  "Sales Pilot",
  "Face Swap",
] as const;

const SAMPLE_IMAGES = [
  "/sample1.jpg",
  "/sample2.jpg",
  "/sample3.jpg",
  "/sample4.jpg",
  "/sample5.jpg",
];

export default function VisibilityAI() {
  const [activeTool, setActiveTool] =
    useState<(typeof TOOLS)[number]>("Background Remover");

  const handleUpload = (file: File) => {
    console.log("Uploaded file:", file);
    // Handle file upload logic here
  };

  return (
    <div className="min-h-screen bg-[#121828] p-8">
      <div className="max-w-6xl mx-auto">
        <Text
          variant="h1"
          className="text-white text-4xl font-semibold text-center mb-8"
        >
          Boost your site's visibility with AI-
          <br />
          Powered Miragic tools
        </Text>

        <div className="flex flex-wrap gap-2 justify-center mb-12">
          {TOOLS.map((tool) => (
            <ToolItem
              key={tool}
              name={tool}
              isActive={tool === activeTool}
              onClick={() => setActiveTool(tool)}
            />
          ))}
        </div>

        <div className="flex flex-col items-center">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-2 h-2 bg-[#2C9BF7] rounded-full" />
            <Text className="text-white font-medium">{activeTool}</Text>
          </div>

          <Text className="text-white/70 text-sm max-w-xl text-center mb-8">
            No matter if you want to make a background transparent (PNG), add a
            white background to a photo, extract or isolate the subject, or get
            the cutout of a photo - you can do all this and more with
            miragic.ai, the AI background remover for professionals.
          </Text>

          <UploadArea
            title={activeTool}
            description="Upload your image to get started"
            acceptedFormats="Original image must be .png, .jpg, .jpeg or webp"
            sampleImages={SAMPLE_IMAGES}
            onUpload={handleUpload}
          />
        </div>
      </div>
    </div>
  );
}
