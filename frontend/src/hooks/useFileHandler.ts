import { useState, useCallback } from 'react';
import BgRemovalService from "@/services/bgRemoval.service";

export interface FileHandlerState {
  previewUrl: string | null;
  uploadedFile: File | null;
  processedImage: string | null;
  error: string | null;
  selectedModel: string | null;
  isProcessing: boolean;
}

const initialState: FileHandlerState = {
  previewUrl: null,
  uploadedFile: null,
  processedImage: null,
  error: null,
  selectedModel: null,
  isProcessing: false
};

export const useFileHandler = () => {
  const [state, setState] = useState<FileHandlerState>(initialState);

  const resetState = useCallback(() => {
    setState(initialState);
  }, []);

  const handleFile = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      const target = e.target as FileReader;
      if (target?.result) {
        setState(prev => ({
          ...prev,
          uploadedFile: file,
          previewUrl: target.result as string,
          selectedModel: null,
          processedImage: null,
          error: null
        }));
      }
    };
    reader.readAsDataURL(file);
  }, []);

  const handleModelSelect = useCallback((modelPath: string) => {
    setState(prev => ({
      ...prev,
      selectedModel: modelPath,
      uploadedFile: null,
      previewUrl: modelPath,
      processedImage: null,
      error: null
    }));
  }, []);

  const handleDownload = useCallback(async () => {
    if (!state.processedImage) return;
    
    try {
      const response = await fetch(state.processedImage);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `bg-removed-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
      setState(prev => ({ ...prev, error: "Failed to download the image" }));
    }
  }, [state.processedImage]);

  const handleProcessImage = useCallback(async () => {
    let fileToProcess: File | null = null;

    try {
      if (state.uploadedFile) {
        fileToProcess = state.uploadedFile;
      } else if (state.selectedModel) {
        const response = await fetch(state.selectedModel);
        const blob = await response.blob();
        fileToProcess = new File([blob], "selected-model.jpg", { type: "image/jpeg" });
      }

      if (!fileToProcess) return;

      setState(prev => ({ ...prev, isProcessing: true, error: null }));
      const response = await BgRemovalService.directRemoveBackgroundHomePage(fileToProcess);

      if (response.success && response.url) {
        setState(prev => ({ ...prev, processedImage: response.url, isProcessing: false }));
      } else {
        throw new Error(response.message || "Failed to remove background");
      }
    } catch (error: unknown) {
      const err = error as { response?: { data?: { error?: { message?: string } } }; message?: string };
      console.error("Error processing image:", err);
      const errorMessage = err.response?.data?.error?.message || err.message || "Unknown error";
      setState(prev => ({ 
        ...prev, 
        error: `Failed to process image: ${errorMessage}`,
        isProcessing: false 
      }));
    }
  }, [state.uploadedFile, state.selectedModel]);

  return {
    state,
    resetState,
    handleFile,
    handleModelSelect,
    handleDownload,
    handleProcessImage
  };
}; 