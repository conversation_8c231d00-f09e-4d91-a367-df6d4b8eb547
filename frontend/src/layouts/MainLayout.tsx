import { Outlet, useLocation } from "react-router-dom";
import { useEffect, useMemo, useRef } from "react";
import NewFooter from "./NewFooter";

import { metaData } from "@/utils/DUMMY_DATA";
import TopBanner from "@/components/header/TopBanner";
import Header from "@/components/header/Header";
import { faqData } from "@/components/common/variable";

function slowScrollToTop(duration = 3000) {
  const start = window.scrollY;
  const startTime = performance.now();

  function scrollStep(currentTime: number) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    window.scrollTo(0, start * (1 - progress));
    if (progress < 1) {
      requestAnimationFrame(scrollStep);
    }
  }

  requestAnimationFrame(scrollStep);
}

const MainLayout = () => {
  const location = useLocation();

  useEffect(() => {
    const meta = metaData[location.pathname];
    if (meta) {
      document.title = meta.title;

      let favicon = document.querySelector(
        "link[rel~='icon']"
      ) as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement("link");
        favicon.rel = "icon";
        document.head.appendChild(favicon);
      }
      favicon.href = meta.favicon;
    }
  }, [location.pathname]);

  const faqSchema = useMemo(() => {
    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      mainEntity: faqData.map((item) => ({
        "@type": "Question",
        name: item.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: item.answer,
        },
      })),
    };
  }, []);

  // Effect for FAQPage for SEO
  useEffect(() => {
    if (faqSchema.mainEntity.length > 0) {
      const script = document.createElement("script");
      script.type = "application/ld+json";
      script.textContent = JSON.stringify(faqSchema);
      script.id = "miragic-ai-schema";
      document.head.appendChild(script);

      // Cleanup function: Remove the script when the component unmounts
      // or dependencies change (though faqSchema is memoized)
      return () => {
        const existingScript = document.getElementById("miragic-ai-schema");
        if (existingScript) {
          document.head.removeChild(existingScript);
        }
      };
    }
  }, [faqSchema]);

  const scrollTopBtnRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const onScroll = () => {
      if (scrollTopBtnRef.current) {
        if (window.scrollY > 50) {
          scrollTopBtnRef.current.style.display = "block";
        } else {
          scrollTopBtnRef.current.style.display = "none";
        }
      }
    };
    window.addEventListener("scroll", onScroll, { passive: true });
    onScroll(); // Set initial state
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  return (
    <div className="flex flex-col min-h-screen">
      <TopBanner />
      <Header />
      <main className="flex-1">
        <Outlet />
      </main>
      <NewFooter />
      <button
        ref={scrollTopBtnRef}
        onClick={() => slowScrollToTop(3000)}
        className="fixed bottom-6 right-6 z-50rounded-full shadow-lg"
        aria-label="Scroll to top"
        style={{ display: "none" }}
      >
        <img
          src="/icons/arrow_up.svg"
          className="w-16 h-16"
          alt="arrow of miragic ai"
        />
      </button>
    </div>
  );
};

export default MainLayout;
