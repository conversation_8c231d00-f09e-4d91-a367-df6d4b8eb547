import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "sonner";
import AdminService from "@/services/admin.service";
import { formatDateToYMD } from "@/lib/utils";
import type { User, UserUpdateData } from "@/types/admin.types";

// Mock user data
const mockUser = {
  id: "usr-001",
  name: "<PERSON>",
  email: "<EMAIL>",
  plan: "Pro",
  joinDate: "May 16, 2025",
  status: "active",
  lastActive: "Today, 10:23 AM",
  credits: 87,
  totalSpent: "$237.00",
  avatar: "https://i.pravatar.cc/150?u=emma",
  phone: "+****************",
  company: "<PERSON> Creative",
  role: "user",
  billingAddress: {
    street: "123 Main St",
    city: "San Francisco",
    state: "CA",
    zip: "94105",
    country: "USA",
  },
};

// Mock billing history
const mockBillingHistory = [
  {
    id: "inv-001",
    date: "May 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-002",
    date: "April 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-003",
    date: "March 15, 2025",
    amount: "$49.00",
    description: "Pro Plan - Monthly",
    status: "paid",
  },
  {
    id: "inv-004",
    date: "May 10, 2025",
    amount: "$20.00",
    description: "Credit Pack - 100 Credits",
    status: "paid",
  },
  {
    id: "inv-005",
    date: "April 28, 2025",
    amount: "$20.00",
    description: "Credit Pack - 100 Credits",
    status: "paid",
  },
];

// Mock usage history
const mockUsageHistory = [
  {
    id: "job-001",
    date: "May 17, 2025, 14:32",
    type: "video",
    description: "Product Explainer Video",
    status: "completed",
    credits: 10,
  },
  {
    id: "job-002",
    date: "May 16, 2025, 11:15",
    type: "image",
    description: "Marketing Banner Generation",
    status: "completed",
    credits: 2,
  },
  {
    id: "job-003",
    date: "May 15, 2025, 16:48",
    type: "bgRemoval",
    description: "Product Image Background Removal",
    status: "completed",
    credits: 1,
  },
  {
    id: "job-004",
    date: "May 14, 2025, 09:27",
    type: "video",
    description: "Company Introduction Video",
    status: "completed",
    credits: 10,
  },
  {
    id: "job-005",
    date: "May 13, 2025, 15:19",
    type: "image",
    description: "Social Media Post Images",
    status: "completed",
    credits: 5,
  },
];

const AdminUserDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editedUser, setEditedUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  console.log(activeTab);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchUser = async () => {
      if (!id) {
        setError("User ID is required");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const userData = await AdminService.getUserById(id);
        setUser(userData);
        setEditedUser(userData);
        setError("");
      } catch (err) {
        console.error("Failed to fetch user:", err);
        setError("Failed to load user data");
        toast.error("Failed to load user data");
        // Fallback to mock data for development
        const fallbackUser: User = {
          ...mockUser,
          id: id || "unknown",
          role: "USER",
          emailVerified: true,
          status: "active",
          createdAt: new Date().toISOString(),
          credit: { balance: 87, spent: 50 },
          totalSpent: 50,
          profile: {
            firstName: "Emma",
            lastName: "Thompson",
            fullName: "Emma Thompson",
            avatarUrl: "https://i.pravatar.cc/150?u=emma",
          },
        };
        setUser(fallbackUser);
        setEditedUser(fallbackUser);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, [id]);

  const handleInputChange = (field: string, value: string) => {
    if (!editedUser) return;

    if (field === "firstName" || field === "lastName") {
      const firstName =
        field === "firstName" ? value : editedUser.profile?.firstName || "";
      const lastName =
        field === "lastName" ? value : editedUser.profile?.lastName || "";
      const fullName = `${firstName} ${lastName}`.trim();

      setEditedUser({
        ...editedUser,
        name: fullName,
        profile: {
          ...editedUser.profile,
          firstName,
          lastName,
          fullName,
          avatarUrl: editedUser.profile?.avatarUrl || "",
        },
      });
    } else if (field === "role") {
      setEditedUser({
        ...editedUser,
        role: value as "USER" | "ADMIN",
      });
    } else if (field === "emailVerified") {
      setEditedUser({
        ...editedUser,
        emailVerified: value === "true",
        status: value === "true" ? "active" : "inactive",
      });
    } else if (field === "creditBalance") {
      setEditedUser({
        ...editedUser,
        credit: {
          ...editedUser.credit,
          balance: parseFloat(value) || 0,
        },
      });
    } else {
      setEditedUser({
        ...editedUser,
        [field]: value,
      });
    }
  };

  const handleSave = async () => {
    if (!editedUser || !user) return;

    try {
      setIsSaving(true);
      const updateData: UserUpdateData = {
        role: editedUser.role,
        emailVerified: editedUser.emailVerified,
        profile: {
          firstName: editedUser.profile?.firstName || "",
          lastName: editedUser.profile?.lastName || "",
          avatarUrl: editedUser.profile?.avatarUrl || "",
        },
        credit: {
          balance: editedUser.credit?.balance || 0,
        },
      };

      const updatedUser = await AdminService.updateUser(user.id, updateData);
      setUser(updatedUser);
      setEditedUser(updatedUser);
      setIsEditing(false);
      toast.success("User updated successfully");
    } catch (err) {
      console.error("Failed to update user:", err);
      toast.error("Failed to update user");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  const handleDeleteUser = async () => {
    if (!user) return;

    if (
      window.confirm(
        "Are you sure you want to delete this user? This action cannot be undone."
      )
    ) {
      try {
        await AdminService.deleteUser(user.id);
        toast.success("User deleted successfully");
        navigate("/admin/users");
      } catch (err) {
        console.error("Failed to delete user:", err);
        toast.error("Failed to delete user");
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Spinner className="mx-auto mb-4" />
          <p className="text-muted-foreground">Loading user details...</p>
        </div>
      </div>
    );
  }

  if (error && !user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={() => navigate("/admin/users")}>
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">User not found</p>
          <Button onClick={() => navigate("/admin/users")}>
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Details</h1>
          <p className="text-muted-foreground">
            View and manage user information
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate("/admin/users")}>
            Back to Users
          </Button>
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)} disabled={isSaving}>
              Edit User
            </Button>
          ) : (
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* User Profile Card */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle>Profile</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div className="w-32 h-32 rounded-full overflow-hidden mb-4">
              <img
                src={user.profile?.avatarUrl || "/api/placeholder/128/128"}
                alt={user.name}
                className="w-full h-full object-cover"
              />
            </div>
            <h3 className="text-xl font-medium">{user.name}</h3>
            <p className="text-muted-foreground">{user.email}</p>
            <div className="mt-2 flex items-center">
              <span
                className={`inline-block w-2 h-2 rounded-full mr-2 ${
                  user.status === "active" ? "bg-green-500" : "bg-red-500"
                }`}
              ></span>
              <span className="capitalize">{user.status}</span>
            </div>
            <div className="mt-4 w-full">
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Role</span>
                <span className="font-medium capitalize">
                  {user.role.toLowerCase()}
                </span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Plan</span>
                <span className="font-medium">{user.plan}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Credits</span>
                <span className="font-medium">{user.credit?.balance || 0}</span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Joined</span>
                <span className="font-medium">
                  {formatDateToYMD(user.createdAt)}
                </span>
              </div>
              <div className="flex justify-between py-2 border-b">
                <span className="text-muted-foreground">Last Active</span>
                <span className="font-medium">
                  {user.lastActive ? formatDateToYMD(user.lastActive) : "Never"}
                </span>
              </div>
              <div className="flex justify-between py-2">
                <span className="text-muted-foreground">Total Spent</span>
                <span className="font-medium">${user.totalSpent || 0}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-2">
            <Button variant="outline" className="w-full">
              Add Credits
            </Button>
            <Button
              variant="destructive"
              className="w-full"
              onClick={handleDeleteUser}
            >
              Delete User
            </Button>
          </CardFooter>
        </Card>

        {/* User Details Tabs */}
        <div className="md:col-span-3">
          <Tabs
            defaultValue="overview"
            className="w-full"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="billing">Billing</TabsTrigger>
              <TabsTrigger value="usage">Usage</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>User Information</CardTitle>
                  <CardDescription>
                    Personal and account details
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">First Name</label>
                      {isEditing ? (
                        <Input
                          value={editedUser?.profile?.firstName || ""}
                          onChange={(e) =>
                            handleInputChange("firstName", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.profile?.firstName || "Not set"}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Last Name</label>
                      {isEditing ? (
                        <Input
                          value={editedUser?.profile?.lastName || ""}
                          onChange={(e) =>
                            handleInputChange("lastName", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.profile?.lastName || "Not set"}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Email</label>
                      <div className="p-2 border rounded-md bg-gray-50">
                        {user.email}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Email cannot be changed
                      </p>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Role</label>
                      {isEditing ? (
                        <select
                          className="w-full p-2 border rounded-md"
                          value={editedUser?.role || "USER"}
                          onChange={(e) =>
                            handleInputChange("role", e.target.value)
                          }
                        >
                          <option value="USER">User</option>
                          <option value="ADMIN">Admin</option>
                        </select>
                      ) : (
                        <div className="p-2 border rounded-md capitalize">
                          {user.role.toLowerCase()}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Email Status
                      </label>
                      {isEditing ? (
                        <select
                          className="w-full p-2 border rounded-md"
                          value={editedUser?.emailVerified ? "true" : "false"}
                          onChange={(e) =>
                            handleInputChange("emailVerified", e.target.value)
                          }
                        >
                          <option value="true">Verified</option>
                          <option value="false">Unverified</option>
                        </select>
                      ) : (
                        <div className="p-2 border rounded-md">
                          <span
                            className={`inline-block w-2 h-2 rounded-full mr-2 ${
                              user.emailVerified ? "bg-green-500" : "bg-red-500"
                            }`}
                          ></span>
                          {user.emailVerified ? "Verified" : "Unverified"}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Credit Balance
                      </label>
                      {isEditing ? (
                        <Input
                          type="number"
                          min="0"
                          step="0.1"
                          value={editedUser?.credit?.balance || 0}
                          onChange={(e) =>
                            handleInputChange("creditBalance", e.target.value)
                          }
                        />
                      ) : (
                        <div className="p-2 border rounded-md">
                          {user.credit?.balance || 0} credits
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="pt-4">
                    <h3 className="text-lg font-medium mb-4">
                      Account Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">User ID</label>
                        <div className="p-2 border rounded-md bg-gray-50 font-mono text-sm">
                          {user.id}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Join Date</label>
                        <div className="p-2 border rounded-md">
                          {formatDateToYMD(user.createdAt)}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Total Spent
                        </label>
                        <div className="p-2 border rounded-md">
                          ${user.totalSpent || 0}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Credits Spent
                        </label>
                        <div className="p-2 border rounded-md">
                          {user.credit?.spent || 0} credits
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                {isEditing && (
                  <CardFooter className="flex justify-end gap-2">
                    <Button variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave}>Save Changes</Button>
                  </CardFooter>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="billing">
              <Card>
                <CardHeader>
                  <CardTitle>Billing History</CardTitle>
                  <CardDescription>
                    View user's billing and payment history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Invoice</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-left py-3 px-4">Description</th>
                          <th className="text-left py-3 px-4">Amount</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockBillingHistory.map((invoice) => (
                          <tr key={invoice.id} className="border-b">
                            <td className="py-3 px-4">{invoice.id}</td>
                            <td className="py-3 px-4">{invoice.date}</td>
                            <td className="py-3 px-4">{invoice.description}</td>
                            <td className="py-3 px-4">{invoice.amount}</td>
                            <td className="py-3 px-4 capitalize">
                              <span
                                className={`inline-block px-2 py-1 rounded-full text-xs ${
                                  invoice.status === "paid"
                                    ? "bg-green-100 text-green-800"
                                    : invoice.status === "pending"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {invoice.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-right">
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline">Download All Invoices</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="usage">
              <Card>
                <CardHeader>
                  <CardTitle>Usage History</CardTitle>
                  <CardDescription>
                    View user's platform usage history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">ID</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-left py-3 px-4">Type</th>
                          <th className="text-left py-3 px-4">Description</th>
                          <th className="text-left py-3 px-4">Credits</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockUsageHistory.map((job) => (
                          <tr key={job.id} className="border-b">
                            <td className="py-3 px-4">{job.id}</td>
                            <td className="py-3 px-4">{job.date}</td>
                            <td className="py-3 px-4 capitalize">
                              {job.type === "video"
                                ? "Video Generation"
                                : job.type === "image"
                                ? "Image Generation"
                                : "Background Removal"}
                            </td>
                            <td className="py-3 px-4">{job.description}</td>
                            <td className="py-3 px-4">{job.credits}</td>
                            <td className="py-3 px-4">
                              <span
                                className={`inline-block px-2 py-1 rounded-full text-xs ${
                                  job.status === "completed"
                                    ? "bg-green-100 text-green-800"
                                    : job.status === "processing"
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {job.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-right">
                              <Button variant="ghost" size="sm">
                                View
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>
                    Manage user account settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Plan Management</h3>
                    <div className="p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">{user.plan} Plan</h4>
                          <p className="text-sm text-muted-foreground">
                            {user.plan === "Free"
                              ? "Limited access to platform features"
                              : user.plan === "Starter"
                              ? "Basic access to all platform features"
                              : user.plan === "Pro"
                              ? "Advanced access with higher usage limits"
                              : "Unlimited access to all platform features"}
                          </p>
                        </div>
                        <Button variant="outline">Change Plan</Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Credits</h3>
                    <div className="p-4 border rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">
                            {user.credits} Credits Available
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Used for pay-as-you-go content generation
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline">Add Credits</Button>
                          <Button variant="outline">Transfer Credits</Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Account Actions</h3>
                    <div className="flex flex-col gap-4">
                      <div className="p-4 border rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium">Reset Password</h4>
                            <p className="text-sm text-muted-foreground">
                              Send a password reset email to the user
                            </p>
                          </div>
                          <Button variant="outline">Send Reset Email</Button>
                        </div>
                      </div>
                      <div className="p-4 border rounded-md">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium">Account Status</h4>
                            <p className="text-sm text-muted-foreground">
                              {user.status === "active"
                                ? "User can currently access the platform"
                                : "User is currently blocked from accessing the platform"}
                            </p>
                          </div>
                          <Button
                            variant={
                              user.status === "active"
                                ? "destructive"
                                : "outline"
                            }
                          >
                            {user.status === "active"
                              ? "Deactivate Account"
                              : "Activate Account"}
                          </Button>
                        </div>
                      </div>
                      <div className="p-4 border rounded-md border-destructive">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="font-medium text-destructive">
                              Delete Account
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Permanently delete this user account and all
                              associated data
                            </p>
                          </div>
                          <Button
                            variant="destructive"
                            onClick={handleDeleteUser}
                          >
                            Delete Account
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AdminUserDetailPage;
