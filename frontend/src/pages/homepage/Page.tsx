// Home component is not being used currently
import ForgotPasswordModal from "@/components/auth/ForgotPasswordModal";
import LoginModal from "@/components/auth/LoginModal";
import SignUpModal from "@/components/auth/SignUpModal";
import FaqSection from "@/components/common/FaqSection";
import FutureSection from "@/components/common/FutureSection";
import BannerSection from "@/components/home/<USER>";
import CustomerReview from "@/components/home/<USER>";
import HeroSection from "@/components/home/<USER>";
// import Home from "@/components/home/<USER>";
import LoveByUser from "@/components/home/<USER>";
import PersonalBrand from "@/components/home/<USER>";
import RelatedArticle from "@/components/home/<USER>";
import VisibilityAI from "@/components/home/<USER>";
import { useState } from "react";

const Page = () => {
  const [isShowSignUpModal, setIsShowSignUpModal] = useState(false);
  const [isShowLoginModal, setIsShowLoginModal] = useState(false);
  const [isShowForgotPassModal, setIsShowForgotPassModal] = useState(false);

  return (
    <div className="overflow-hidden bg-[#121828] lg:space-y-24 space-y-16">
      <HeroSection setIsShowSignUpModal={setIsShowSignUpModal} />
      <VisibilityAI />
      <BannerSection />
      <PersonalBrand />
      <LoveByUser />
      <CustomerReview />
      <RelatedArticle />
      <div className="px-4 sm:px-6 lg:px-8 2xl:px-[120px] w-full max-w-[1440px] mx-auto">
        <FaqSection initialCount={3} />
      </div>
      <FutureSection />

      <LoginModal
        isOpen={isShowLoginModal}
        onClose={() => setIsShowLoginModal(false)}
        onClickSignUpModal={() => {
          setIsShowLoginModal(false);
          setIsShowForgotPassModal(false);
          setIsShowSignUpModal(true);
        }}
        onClickForgotPassword={() => {
          setIsShowLoginModal(false);
          setIsShowSignUpModal(false);
          setIsShowForgotPassModal(true);
        }}
      />
      <SignUpModal
        isOpen={isShowSignUpModal}
        onClose={() => setIsShowSignUpModal(false)}
        onClickLoginModal={() => {
          setIsShowLoginModal(true);
          setIsShowSignUpModal(false);
        }}
      />
      <ForgotPasswordModal
        isOpen={isShowForgotPassModal}
        onClose={() => setIsShowForgotPassModal(false)}
        onBack={() => {
          setIsShowForgotPassModal(false);
          setIsShowLoginModal(true);
        }}
      />
    </div>
  );
};

export default Page;
