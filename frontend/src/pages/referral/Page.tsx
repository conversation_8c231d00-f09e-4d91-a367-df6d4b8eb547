import LandingPageFAQ from "@/components/home/<USER>";
import Section from "@/components/layout/Section";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";

const Page = () => {
  return (
    <div className="relative  bg-black">
      <img
        className="absolute top-0 left-0 opacity-25 pointer-events-none"
        src="/png/pattern_1.png"
      />

      <img
        className="absolute top-0 right-0 w-3/5 z-[5px] pointer-events-none"
        src="/png/referrel_right_shadow.png"
      />
      {/* <img
        className="absolute transform scale-y-[-1] translate-y-1/2  top-1/2  right-0 w-4/5 z-[5px]"
        src="/png/shadow_hero_sec.png"
      /> */}

      <Section className="pb-[100px]">
        <div className="flex z-10 pt-28 flex-col  gap-6 justify-center items-center">
          <div className="flex flex-col gap-5 max-w-[990px] justify-center items-center">
            <Text
              font="Inter"
              variant={"page_title"}
              className="bg-clip-text text-transparent bg-gradient-to-l from-white via-white to-gray-500"
            >
              MiragicAI Referral Program
            </Text>
            <Text variant={"card_title_large"} className="text-center">
              MiragicAI reserves the right to revoke credits or ban accounts if
              fraudulent activity is detected. We may also adjust the program,
              including reward rules or duration, at any time.
            </Text>
          </div>
          <Button
            outline={false}
            variant={"animeShine"}
            className="z-10 min-w-[235px] h-[50px]"
          >
            Start referring!
          </Button>
          <div className="flex relative flex-row rounded-[25px] w-full z-10 p-16 border border-[#282729] bg-[#2A1946]">
            {/* <!-- Your row content here --> */}
            <img
              src="/png/referrals_img.png"
              className="absolute right-16 bottom-0"
            />
            <div className="flex flex-col gap-8 max-w-[550px]">
              <Text variant={"page_title"} className="text-white">
                Refer and earn exciting prizes 🔥{" "}
              </Text>
              <Text variant={"body"}>
                It is a long established fact that a reader will be distracted
                by the readable content of a page when looking at its layout.
              </Text>
            </div>
          </div>
        </div>
        <HowItWorkSection />
        <LandingPageFAQ />
      </Section>
    </div>
  );
};

const HowItWorkSection = () => {
  return (
    <div className="relative flex flex-col gap-16 py-[100px] ">
      <img
        className="absolute -top-1/2  right-1/2 transform translate-x-1/2 w-full z-[5px] pointer-events-none"
        src="/png/referrel_middle_shadow.png"
      />
      <Text
        font="Inter"
        variant={"page_title"}
        className="bg-clip-text text-center text-transparent bg-gradient-to-l from-white via-white to-gray-500"
      >
        How it works
      </Text>
      <div className="flex flex-col gap-8">
        <div className=" grid place-items-start grid-cols-7 gap-8 p-8 flex-row rounded-[25px] border border-[rgba(255,255,255,0.27)] bg-[rgba(255,255,255,0.06)]">
          <div className="col-span-4 flex flex-col gap-12 justify-between">
            <div className="flex items-center gap-8 justify-between">
              <Text variant={"card_title_large"} className="text-white">
                $10 Discount
              </Text>
              <Text variant={"card_title_large"} className="text-white">
                +
              </Text>
              <Text variant={"card_title_large"} className="text-white">
                30% Discount
              </Text>
            </div>
            <div className="flex items-center gap-8 justify-between">
              <Button
                variant={"gradient"}
                className="min-w-[235px] min-h-[60px] rounded-full"
              >
                Inviter
              </Button>
              <Button
                variant={"gradient"}
                className="min-w-[235px] min-h-[60px] rounded-full"
              >
                Inviter
              </Button>
            </div>
          </div>
          <div className="col-span-3 flex flex-col justify-center items-center gap-8">
            <img src="/png/referral_discount_1.png" className="max-w-[150px]" />
            <Text variant={"card_body"} className="text-left">
              Earn a $10 discount reward every time a friend registers and uses
              4 Credits. Get a 30% discount reward on your next purchase based
              on your friend’s subscription fee.
            </Text>
          </div>
        </div>
        <div className=" grid place-items-start grid-cols-7 gap-8 p-8 flex-row rounded-[25px] border border-[rgba(255,255,255,0.27)] bg-[rgba(255,255,255,0.06)]">
          <div className="col-span-4 flex flex-col gap-12 justify-between">
            <div className="flex items-center gap-8 justify-between">
              <Text variant={"card_title_large"} className="text-white">
                100 Credits
              </Text>
              <Text variant={"card_title_large"} className="text-white">
                +
              </Text>
              <Text variant={"card_title_large"} className="text-white">
                30% Discount
              </Text>
            </div>
            <div className="flex items-center gap-8 justify-between">
              <Button
                variant={"gradient"}
                className="min-w-[235px] min-h-[60px] rounded-full"
              >
                Person Invited
              </Button>
              <Button
                variant={"gradient"}
                className="min-w-[235px] min-h-[60px] rounded-full"
              >
                Person Invited
              </Button>
            </div>
          </div>
          <div className="col-span-3 flex flex-col justify-center items-center gap-8">
            <img src="/png/referral_discount_1.png" className="max-w-[150px]" />
            <Text variant={"card_body"} className="text-left">
              Earn a $10 discount reward every time a friend registers and uses
              4 Credits. Get a 30% discount reward on your next purchase based
              on your friend’s subscription fee.
            </Text>
          </div>
        </div>
      </div>

      {/* <img
      {/* */}
    </div>
  );
};

export default Page;
