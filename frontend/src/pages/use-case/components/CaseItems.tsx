import Section from "@/components/layout/Section";
import { Text } from "@/components/ui/text";
import { ChevronLeft, ChevronRight } from "lucide-react";

const data = [
  {
    index: 1,
    tag: "Virtual Try-On",
    title:
      "Anyone can be fashion model wearing virtual clothes released by MiragicAI",
    description:
      "With Virtual Try-on technology, anyone can become a fashion model by effortlessly showcasing digital outfits. This innovative tool lets users try on virtual clothes in real-time, transforming the way we experience fashion.",
    img: "/png/use_case_1.png",
  },
  {
    index: 2,
    tag: "Virtual Try-On",
    title: "Transform Your Fashion Photography Today",
    description:
      "Anyone can experience the future of fashion visualization with our AI-powered virtual try-on solution. Save time, reduce costs, and create stunning outfit visuals instantly!",
    img: "/png/use_case_2.png",
  },
  {
    index: 3,
    tag: "Background Remover",
    title:
      "How do brands achieve studio-quality images effortlessly with Miragic",
    description:
      "See how companies use AI background removal and image upscaling to create high-resolution visuals that boost engagement and reduce manual editing time.",
    img: "/png/use_case_3.png",
  },
  {
    index: 4,
    tag: "Background Remover",
    title: "How does Miragic help e-commerce teams scale visual content?",
    description:
      "Discover how retailers streamline product photography using automated background removal and high-resolution enhancement, delivering faster, cleaner, and more professional images.",
    img: "/png/use_case_4.png",
  },
  {
    index: 5,
    tag: "Speed Painting",
    title: "Speed Painting on Canva",
    description:
      "Anyone can elevate their own designs by adding custom illustrations and time-lapse videos of their artwork directly into their Canva projects. Mirgic helps you to start creating with Speedpaint in Canva today!",
    img: "/png/use_case_5.png",
  },
  {
    index: 6,
    tag: "Speed Painting",
    title: "How does our Speedpaint work?",
    description:
      "Speedpaint uses a neural network trained on thousands of real whiteboard animation videos to recreate your image as a seamless hand-drawn sequence in full HD, giving it an authentic sketch-on-whiteboard look.",
    img: "/png/use_case_6.png",
  },
  {
    index: 7,
    tag: "Speed Painting",
    title: "Why do customers need Speed Painting tool?",
    description:
      "SpeedPaint makes your content stand out in a sea of sameness. The ability to turn any image into a sleek whiteboard animation-style video is amazing. It’s user-friendly and consistently produces high-quality results. You can’t imagine creating without it now!",
    img: "/png/use_case_7.png",
  },
  {
    index: 8,
    tag: "Speed Painting",
    title: "How does Miragic help e-commerce teams scale visual content?",
    description:
      "Discover how retailers streamline product photography using automated background removal and high-resolution enhancement, delivering faster, cleaner, and more professional images.",
    img: "/png/use_case_8.png",
  },
  {
    index: 9,
    tag: "Virtual Try-On",
    title:
      "Anyone can be fashion model wearing virtual clothes released by MiragicAI",
    description:
      "With Virtual Try-on technology, anyone can become a fashion model by effortlessly showcasing digital outfits. This innovative tool lets users try on virtual clothes in real-time, transforming the way we experience fashion.",
    img: "/png/use_case_9.png",
  },
];

const CaseItems = () => {
  return (
    <Section className="py-20">
      <div className="flex flex-col gap-14">
        <div className="w-full  pb-20 grid grid-cols-1 sm:grid-cols-2 gap-y-8 gap-x-6 md:grid-cols-3">
          {data.map((item, index) => {
            return <CaseItem key={index} data={item} />;
          })}
          {/* <CaseItem data={}/> */}
        </div>
        <div className="flex w-full justify-between items-center">
          <div></div>
          <div className="flex items-center gap-8">
            <ChevronLeft className="text-[32px] text-white" />
            <div className="h-10 w-10 bg-white flex items-center text-center justify-center rounded-2xl">
              <Text variant={"body"} className="text-center text-gray-950">
                1
              </Text>
            </div>
            <ChevronRight className="text-[32px] text-white" />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-10 w-10  border-border border-2 flex items-center text-center justify-center rounded-2xl">
              <Text variant={"body"} className="text-center text-white">
                1
              </Text>
            </div>{" "}
            <Text variant={"body"} className="text-center text-gray-400">
              / 1
            </Text>
          </div>{" "}
        </div>
      </div>
    </Section>
  );
};
// {
//     index: 1,
//     tag: "Virtual Try-On",
//     title: "Anyone can be fashion model wearing virtual clothes released by MiragicAI",
//     description: "With Virtual Try-on technology, anyone can become a fashion model by effortlessly showcasing digital outfits. This innovative tool lets users try on virtual clothes in real-time, transforming the way we experience fashion.",
//     img: "/png/use_case_1.png"
//   }
const CaseItem = ({
  data,
}: {
  data: {
    index: number;
    tag: string;
    title: string;
    description: string;
    img: string;
  };
}) => {
  return (
    <div
      style={{
        background:
          "linear-gradient(#1f2937, #1f2937)  padding-box, linear-gradient(to bottom, #A166FC, #999999) border-box",
        border: "1px solid transparent",
      }}
      className="rounded-[25px] border-[3px] border-[#A166FC] bg-[rgba(255,255,255,0.06)] flex z-10 relative overflow-hidden flex-col text-white  shadow-md w-full  "
    >
      <div className="absolute  bg-[#D9D9D9]/36 top-4 right-6 px-5 py-1 rounded-sm">
        <Text variant={"card_body"} className="text-white font-medium">
          {data.tag}
        </Text>
      </div>
      <img
        className="w-full h-[350px] object-cover overflow-hidden rounded-xl"
        src={data.img}
      />
      <div className="flex flex-col gap-5 p-5">
        <div className="flex flex-col gap-2">
          <Text
            variant={"card_body"}
            className="text-[16px] text-white font-semibold "
          >
            {data.title}
          </Text>
          <Text
            variant={"card_body"}
            className="text-[14px] font-light line-clamp-5"
          >
            {data.description}
          </Text>
        </div>
      </div>
    </div>
  );
};
export default CaseItems;
